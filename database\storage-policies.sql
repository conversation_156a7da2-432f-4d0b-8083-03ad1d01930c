-- Criar bucket se não existir
INSERT INTO storage.buckets (id, name, public)
VALUES ('agosto25', 'agosto25', true)
ON CONFLICT (id) DO NOTHING;

-- Remover políticas existentes se houver
DROP POLICY IF EXISTS "Allow public uploads" ON storage.objects;
DROP POLICY IF EXISTS "Allow public access" ON storage.objects;
DROP POLICY IF EXISTS "Allow public deletes" ON storage.objects;

-- Política para permitir uploads públicos no bucket agosto25
CREATE POLICY "Allow public uploads" ON storage.objects
  FOR INSERT WITH CHECK (bucket_id = 'agosto25');

-- Política para permitir acesso público aos arquivos no bucket agosto25
CREATE POLICY "Allow public access" ON storage.objects
  FOR SELECT USING (bucket_id = 'agosto25');

-- Política para permitir exclusão pública no bucket agosto25
CREATE POLICY "Allow public deletes" ON storage.objects
  FOR DELETE USING (bucket_id = 'agosto25');

-- Política para permitir atualizações públicas no bucket agosto25
CREATE POLICY "Allow public updates" ON storage.objects
  FOR UPDATE USING (bucket_id = 'agosto25');

-- Verificar se o bucket foi criado corretamente
SELECT * FROM storage.buckets WHERE id = 'agosto25';