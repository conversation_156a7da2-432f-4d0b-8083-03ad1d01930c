import type { Config } from "tailwindcss";

export default {
	darkMode: ["class"],
	content: [
		"./pages/**/*.{ts,tsx}",
		"./components/**/*.{ts,tsx}",
		"./app/**/*.{ts,tsx}",
		"./src/**/*.{ts,tsx}",
		"./index.html"
	],
	prefix: "",
	theme: {
		container: {
			center: true,
			padding: '2rem',
			screens: {
				'2xl': '1400px'
			}
		},
		extend: {
			colors: {
				// Cores do sistema shadcn/ui
				border: 'hsl(var(--border))',
				input: 'hsl(var(--input))',
				ring: 'hsl(var(--ring))',
				background: 'hsl(var(--background))',
				foreground: 'hsl(var(--foreground))',
				primary: {
					DEFAULT: 'hsl(var(--primary))',
					foreground: 'hsl(var(--primary-foreground))'
				},
				secondary: {
					DEFAULT: 'hsl(var(--secondary))',
					foreground: 'hsl(var(--secondary-foreground))'
				},
				destructive: {
					DEFAULT: 'hsl(var(--destructive))',
					foreground: 'hsl(var(--destructive-foreground))'
				},
				muted: {
					DEFAULT: 'hsl(var(--muted))',
					foreground: 'hsl(var(--muted-foreground))'
				},
				accent: {
					DEFAULT: 'hsl(var(--accent))',
					foreground: 'hsl(var(--accent-foreground))'
				},
				popover: {
					DEFAULT: 'hsl(var(--popover))',
					foreground: 'hsl(var(--popover-foreground))'
				},
				card: {
					DEFAULT: 'hsl(var(--card))',
					foreground: 'hsl(var(--card-foreground))'
				},
				sidebar: {
					DEFAULT: 'hsl(var(--sidebar-background))',
					foreground: 'hsl(var(--sidebar-foreground))',
					primary: 'hsl(var(--sidebar-primary))',
					'primary-foreground': 'hsl(var(--sidebar-primary-foreground))',
					accent: 'hsl(var(--sidebar-accent))',
					'accent-foreground': 'hsl(var(--sidebar-accent-foreground))',
					border: 'hsl(var(--sidebar-border))',
					ring: 'hsl(var(--sidebar-ring))'
				},

				// Magazine theme colors matching CSS custom properties
				magazine: {
					DEFAULT: 'hsl(var(--newsletter-green))',
					light: 'hsl(var(--newsletter-light))',
					hover: 'hsl(var(--newsletter-green-hover))',
					'light-hover': 'hsl(var(--newsletter-light-hover))'
				},
				
				// Cores personalizadas para Lubenham News
				'lubenham': {
					50: '#f0fdf4',
					100: '#dcfce7',
					200: '#bbf7d0',
					300: '#86efac',
					400: '#4ade80',
					500: '#22c55e', // Primary green
					600: '#16a34a', // Primary dark
					700: '#15803d',
					800: '#166534',
					900: '#14532d',
					950: '#052e16'
				},
				'cream': {
					50: '#fffef7',
					100: '#fefce8',
					200: '#fef3c7', // Accent cream
					300: '#fde68a',
					400: '#fcd34d',
					500: '#fbbf24',
					600: '#f59e0b',
					700: '#d97706',
					800: '#b45309',
					900: '#92400e',
					950: '#78350f'
				},
				
				// Glass morphism
				'glass': 'rgba(255, 255, 255, 0.1)',
				'glass-dark': 'rgba(0, 0, 0, 0.1)'
			},
			
			borderRadius: {
				lg: 'var(--radius)',
				md: 'calc(var(--radius) - 2px)',
				sm: 'calc(var(--radius) - 4px)'
			},
			
			fontFamily: {
				sans: ['Inter', 'system-ui', 'sans-serif'],
				serif: ['Georgia', 'serif'],
				mono: ['JetBrains Mono', 'monospace']
			},
			
			fontSize: {
				'magazine-title': ['4rem', { lineHeight: '1.1', fontWeight: '800' }],
				'magazine-subtitle': ['1.5rem', { lineHeight: '1.4', fontWeight: '400' }],
				'page-title': ['2rem', { lineHeight: '1.2', fontWeight: '700' }]
			},
			
			spacing: {
				'18': '4.5rem',
				'88': '22rem',
				'128': '32rem'
			},
			
			backdropBlur: {
				xs: '2px'
			},
			
			keyframes: {
				'accordion-down': {
					from: { height: '0' },
					to: { height: 'var(--radix-accordion-content-height)' }
				},
				'accordion-up': {
					from: { height: 'var(--radix-accordion-content-height)' },
					to: { height: '0' }
				},
				'fade-in': {
					from: { opacity: '0', transform: 'translateY(10px)' },
					to: { opacity: '1', transform: 'translateY(0)' }
				},
				'slide-in-right': {
					from: { transform: 'translateX(100%)' },
					to: { transform: 'translateX(0)' }
				},
				'pulse-slow': {
					'0%, 100%': { opacity: '1' },
					'50%': { opacity: '0.5' }
				},
				'bounce-gentle': {
					'0%, 100%': { transform: 'translateY(0)' },
					'50%': { transform: 'translateY(-5px)' }
				}
			},
			
			animation: {
				'accordion-down': 'accordion-down 0.2s ease-out',
				'accordion-up': 'accordion-up 0.2s ease-out',
				'fade-in': 'fade-in 0.5s ease-out',
				'slide-in-right': 'slide-in-right 0.3s ease-out',
				'pulse-slow': 'pulse-slow 3s ease-in-out infinite',
				'bounce-gentle': 'bounce-gentle 2s ease-in-out infinite'
			},
			
			boxShadow: {
				'glass': '0 8px 32px 0 rgba(31, 38, 135, 0.37)',
				'magazine': '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
				'page': '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)'
			},
			
			screens: {
				'xs': '475px',
				'3xl': '1600px',
				'4xl': '1920px',
				'5xl': '2560px',
				'6xl': '3440px'
			},

			maxWidth: {
				'8xl': '88rem',
				'9xl': '96rem',
				'10xl': '120rem',
				'screen-4xl': '1920px',
				'screen-5xl': '2560px',
				'screen-6xl': '3440px'
			}
		}
	},
	plugins: [
		require("tailwindcss-animate"),
		require("@tailwindcss/typography")
	],
} satisfies Config;
