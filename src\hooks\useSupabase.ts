import { useState, useEffect } from 'react';
import { supabase } from '../lib/supabase';
import type { PostgrestError } from '@supabase/supabase-js';

interface UseSupabaseQuery<T> {
    data: T[] | null;
    loading: boolean;
    error: PostgrestError | null;
    refetch: () => Promise<void>;
}

export function useSupabaseQuery<T = any>(
    table: string,
    query?: string
): UseSupabaseQuery<T> {
    const [data, setData] = useState<T[] | null>(null);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<PostgrestError | null>(null);

    const fetchData = async () => {
        try {
            setLoading(true);
            setError(null);

            let queryBuilder = supabase.from(table).select(query || '*');

            const { data: result, error: queryError } = await queryBuilder;

            if (queryError) {
                setError(queryError);
                setData(null);
            } else {
                // Ensure result is an array or null
                if (Array.isArray(result) || result === null) {
                    setData(result as T[] | null);
                } else {
                    console.warn('Unexpected data format from Supabase:', result);
                    setData(null);
                }
            }
        } catch (err) {
            console.error('Error fetching data:', err);
            setError(err as PostgrestError);
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        fetchData();
    }, [table, query]);

    return {
        data,
        loading,
        error,
        refetch: fetchData
    };
}

export function useSupabaseInsert<T = any>(table: string) {
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState<PostgrestError | null>(null);

    const insert = async (data: Partial<T>) => {
        try {
            setLoading(true);
            setError(null);

            const { data: result, error: insertError } = await supabase
                .from(table)
                .insert(data)
                .select();

            if (insertError) {
                setError(insertError);
                return null;
            }

            return result;
        } catch (err) {
            console.error('Error inserting data:', err);
            setError(err as PostgrestError);
            return null;
        } finally {
            setLoading(false);
        }
    };

    return {
        insert,
        loading,
        error
    };
}

export function useSupabaseUpdate<T = any>(table: string) {
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState<PostgrestError | null>(null);

    const update = async (id: string | number, data: Partial<T>) => {
        try {
            setLoading(true);
            setError(null);

            const { data: result, error: updateError } = await supabase
                .from(table)
                .update(data)
                .eq('id', id)
                .select();

            if (updateError) {
                setError(updateError);
                return null;
            }

            return result;
        } catch (err) {
            console.error('Error updating data:', err);
            setError(err as PostgrestError);
            return null;
        } finally {
            setLoading(false);
        }
    };

    return {
        update,
        loading,
        error
    };
}

export function useSupabaseDelete(table: string) {
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState<PostgrestError | null>(null);

    const deleteRecord = async (id: string | number) => {
        try {
            setLoading(true);
            setError(null);

            const { error: deleteError } = await supabase
                .from(table)
                .delete()
                .eq('id', id);

            if (deleteError) {
                setError(deleteError);
                return false;
            }

            return true;
        } catch (err) {
            console.error('Error deleting data:', err);
            setError(err as PostgrestError);
            return false;
        } finally {
            setLoading(false);
        }
    };

    return {
        deleteRecord,
        loading,
        error
    };
}