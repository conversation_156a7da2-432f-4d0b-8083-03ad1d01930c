import React, { useState, useEffect, useRef } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { ExternalLink, Phone, Mail, MapPin, ChevronDown, ChevronUp } from 'lucide-react';

interface Advertisement {
  id: string;
  title: string;
  description: string;
  image?: string;
  contact?: {
    phone?: string;
    email?: string;
    website?: string;
    address?: string;
  };
  type: 'business' | 'event' | 'service';
}

const advertisements: Advertisement[] = [
  {
    id: '1',
    title: 'Marston Steam & Vintage Show',
    description: 'Steam engines, vintage tractors, classic cars, and family fun!',
    contact: {
      phone: '************',
      website: 'www.marstonsteamshow.co.uk',
      address: 'Marston Trussell, Market Harborough, LE16 9TU'
    },
    type: 'event'
  },
  {
    id: '2',
    title: 'Complete Car Care',
    description: 'Bodywork specialist - MOT repairs, wheel refurbishment, classic restoration',
    contact: {
      phone: '************',
      address: 'Unit 7, The Oaks, Gumley Road, Theddingworth, LE17 6QJ'
    },
    type: 'business'
  },
  {
    id: '3',
    title: 'The Coach & Horses',
    description: 'Traditional pub with great food, entertainment, and community events',
    contact: {
      phone: '************',
      email: '<EMAIL>',
      website: 'www.coachandhorseslubenham.co.uk'
    },
    type: 'business'
  },
  {
    id: '4',
    title: 'Lloyd Harden Architectural Services',
    description: 'Planning drawings, house extensions, barn conversions, and interior design',
    contact: {
      phone: '0116 269 9534',
      email: '<EMAIL>',
      website: 'www.lloydharden.co.uk',
      address: '36 Main Street, Lubenham'
    },
    type: 'service'
  },
  {
    id: '5',
    title: 'Blackberry Hill Carpets',
    description: 'Quality carpets and flooring solutions for your home',
    contact: {
      phone: '************ or ************',
      email: '<EMAIL>',
      address: 'Unit 1, 78 St Mary\'s Road, Market Harborough'
    },
    type: 'business'
  },
  {
    id: '6',
    title: 'Lubenham Village Store',
    description: 'Local convenience store with fresh produce, newspapers, and postal services',
    contact: {
      phone: '************',
      address: 'High Street, Lubenham, LE16 9TF'
    },
    type: 'business'
  },
  {
    id: '7',
    title: 'Green Thumb Garden Services',
    description: 'Professional gardening, landscaping, and tree surgery services',
    contact: {
      phone: '************',
      email: '<EMAIL>',
      address: 'Serving Lubenham and surrounding areas'
    },
    type: 'service'
  },
  {
    id: '8',
    title: 'Welland Valley Windows',
    description: 'Double glazing, conservatories, and home improvement specialists',
    contact: {
      phone: '************',
      email: '<EMAIL>',
      website: 'www.wellandvalleywindows.co.uk',
      address: 'Market Harborough Industrial Estate'
    },
    type: 'business'
  },
  {
    id: '9',
    title: 'Countryside Veterinary Practice',
    description: 'Caring for your pets with modern facilities and experienced vets',
    contact: {
      phone: '************',
      email: '<EMAIL>',
      website: 'www.countrysidevet.co.uk',
      address: 'Kibworth Road, Market Harborough'
    },
    type: 'service'
  },
  {
    id: '10',
    title: 'Harborough Heating Solutions',
    description: 'Boiler repairs, central heating installation, and plumbing services',
    contact: {
      phone: '************',
      email: '<EMAIL>',
      address: 'Covering Lubenham and Market Harborough'
    },
    type: 'service'
  },
  {
    id: '11',
    title: 'The Old Bakery Tea Rooms',
    description: 'Homemade cakes, light lunches, and traditional afternoon tea',
    contact: {
      phone: '************',
      address: 'Church Lane, Lubenham, LE16 9TG'
    },
    type: 'business'
  },
  {
    id: '12',
    title: 'Leicestershire Mobile Hairdressing',
    description: 'Professional hair styling in the comfort of your own home',
    contact: {
      phone: '************',
      email: '<EMAIL>'
    },
    type: 'service'
  },
  {
    id: '13',
    title: 'Market Harborough Taxis',
    description: '24/7 reliable taxi service for local and long-distance journeys',
    contact: {
      phone: '************',
      email: '<EMAIL>',
      website: 'www.marketharboroughtaxis.co.uk'
    },
    type: 'service'
  },
  {
    id: '14',
    title: 'Flow Pilates with Louise',
    description: 'Mixed ability and advanced Pilates classes for all fitness levels',
    contact: {
      phone: '************',
      email: '<EMAIL>',
      address: 'Village Hall, Lubenham'
    },
    type: 'service'
  },
  {
    id: '15',
    title: 'The Dojo - Martial Arts',
    description: 'Aikido classes for adults and children, professional instruction',
    contact: {
      phone: '************',
      email: '<EMAIL>',
      address: 'The Hollies, 28 Main Street, Lubenham'
    },
    type: 'service'
  },
  {
    id: '16',
    title: 'Café Onyx',
    description: 'Community café with regular coffee mornings and social events',
    contact: {
      phone: '************',
      email: '<EMAIL>',
      address: 'Village Hall, Lubenham'
    },
    type: 'business'
  },
  {
    id: '17',
    title: 'Foxton Community Choir',
    description: 'Weekly choir practice, all voices welcome, no auditions required',
    contact: {
      phone: '************',
      email: '<EMAIL>',
      address: 'Robert Monk Hall, Foxton'
    },
    type: 'service'
  },
  {
    id: '18',
    title: 'Foxton Gardens Society',
    description: 'Gardening society with monthly meetings and garden visits',
    contact: {
      phone: '************',
      email: '<EMAIL>',
      address: 'Church, Foxton'
    },
    type: 'service'
  },
  {
    id: '19',
    title: 'Happy Days Dance Fitness',
    description: 'Fun dance fitness classes for all ages and abilities',
    contact: {
      phone: '************',
      website: 'happydaysfitness.co.uk',
      address: 'Village Hall, Lubenham'
    },
    type: 'service'
  },
  {
    id: '20',
    title: 'Lubenham Art Group',
    description: 'Weekly art sessions, all skill levels welcome, materials provided',
    contact: {
      phone: '************',
      email: '<EMAIL>',
      address: 'Village Hall, Lubenham'
    },
    type: 'service'
  },
  {
    id: '21',
    title: 'Em\'s Hair Studio',
    description: 'Unisex hair salon - cutting & colouring specialist, all Wella products',
    contact: {
      phone: '************',
      address: 'Professional hairdressing services'
    },
    type: 'business'
  },
  {
    id: '22',
    title: 'P. Felton Carpentry & Joinery',
    description: 'All aspects of carpentry and quality purpose-made joinery in soft and hard woods',
    contact: {
      phone: '************',
      email: '<EMAIL>',
      address: '4 Heygate Street, Market Harborough, LE16 7JS'
    },
    type: 'service'
  },
  {
    id: '23',
    title: 'Seasoned Logs - Robert',
    description: 'All hardwood logs £90 per ton, kindling 3 nets for £10, free local delivery',
    contact: {
      phone: '************',
      address: 'Free delivery within 5 mile radius of Lubenham'
    },
    type: 'business'
  },
  {
    id: '24',
    title: 'Abel Electrical (UK) Ltd',
    description: 'Electrical installation contractors - industrial, agricultural, commercial, domestic',
    contact: {
      phone: '************ or ************',
      address: 'Lubenham'
    },
    type: 'service'
  },
  {
    id: '25',
    title: 'Woodpecker Tree Surgery',
    description: 'All aspects of commercial and residential tree work, pruning and reshaping',
    contact: {
      phone: '************ or ************',
      website: 'www.woodpeckertreesurgery.co.uk',
      address: 'Vine Lodge, Hothorpe Road, Marston Trussell LE16 9TX'
    },
    type: 'service'
  },
  {
    id: '26',
    title: 'Mayfield & Co Chartered Accountants',
    description: 'Free initial consultation worth £200, professional accounting services',
    contact: {
      phone: '************',
      website: 'www.mayfieldandco.co.uk',
      address: 'Harborough Innovation Centre, Market Harborough LE16 7WB'
    },
    type: 'service'
  },
  {
    id: '27',
    title: 'Jim Burbidge Excavation & Groundwork',
    description: 'Professional excavation and groundwork services for all projects',
    contact: {
      phone: '01858 463382',
      address: 'Lubenham area'
    },
    type: 'service'
  },
  {
    id: '28',
    title: 'M.Page Building Contractor Ltd',
    description: 'Catering for all your building requirements with professional service',
    contact: {
      phone: '************',
      address: '24 Foxton Road, Lubenham, LE16 9TB'
    },
    type: 'service'
  },
  {
    id: '29',
    title: 'Gore Lodge Guest House',
    description: 'Comfortable guest house accommodation in the heart of Lubenham',
    contact: {
      phone: '************',
      address: '1 School Lane, Lubenham, LE16 9TW'
    },
    type: 'business'
  },
  {
    id: '30',
    title: 'J.Unwin Plumbing & Heating',
    description: 'Quality, friendly and professional plumbing and heating services',
    contact: {
      phone: '************ or ************',
      website: 'www.unwinplumbing.co.uk',
      address: 'Local area coverage'
    },
    type: 'service'
  },
  {
    id: '31',
    title: 'Cleaning Time',
    description: '14+ years experience in carpet, upholstery, driveway and tile cleaning',
    contact: {
      phone: '************ or ************',
      address: 'Local professional company'
    },
    type: 'service'
  },
  {
    id: '32',
    title: 'Computer Help - Rob',
    description: 'Professional computer support and IT assistance services',
    contact: {
      phone: '************',
      email: '<EMAIL>',
      address: 'Tower Court, Lubenham'
    },
    type: 'service'
  },
  {
    id: '33',
    title: 'Andrea Castle Care Services',
    description: 'Personal care, palliative care assistance, companionship, NVQ qualified',
    contact: {
      phone: '07751 193346 or 01858 289544',
      address: 'Passionate about care services'
    },
    type: 'service'
  },
  {
    id: '34',
    title: 'JB Landscaping & Construction',
    description: 'Brickwork, slabbing, concreting, fencing, groundwork, general DIY',
    contact: {
      phone: '07495 970806',
      email: '<EMAIL>',
      address: 'No job too small - Nathanial Jackson-Bream'
    },
    type: 'service'
  },
  {
    id: '35',
    title: 'Lubenham Village Hall',
    description: 'Main Hall, Onyx Room and Committee Room available for hire',
    contact: {
      phone: '01858 465892',
      email: '<EMAIL>',
      address: 'Private parties or regular groups, local residents reduced rates'
    },
    type: 'service'
  },
  {
    id: '36',
    title: 'Lubenham Playing Fields - Cartridge Collection',
    description: 'Collecting used printer cartridges to support Lubenham Playing Fields',
    contact: {
      address: 'Drop off at 1 Mill Hill LE16 9TH, Lubenham Church, or Cafe Onyx'
    },
    type: 'service'
  }
];

export const Sidebar: React.FC = () => {
  const [showScrollTop, setShowScrollTop] = useState(false);
  const [showScrollBottom, setShowScrollBottom] = useState(true);
  const scrollContainerRef = useRef<HTMLDivElement>(null);

  const handleScroll = () => {
    if (scrollContainerRef.current) {
      const { scrollTop, scrollHeight, clientHeight } = scrollContainerRef.current;
      setShowScrollTop(scrollTop > 20);
      setShowScrollBottom(scrollTop < scrollHeight - clientHeight - 20);
    }
  };

  const scrollToTop = () => {
    scrollContainerRef.current?.scrollTo({ top: 0, behavior: 'smooth' });
  };

  const scrollToBottom = () => {
    scrollContainerRef.current?.scrollTo({
      top: scrollContainerRef.current.scrollHeight,
      behavior: 'smooth'
    });
  };

  useEffect(() => {
    const container = scrollContainerRef.current;
    if (container) {
      container.addEventListener('scroll', handleScroll);
      // Initial check
      handleScroll();
      return () => container.removeEventListener('scroll', handleScroll);
    }
  }, []);

  return (
    <div className="h-full flex flex-col relative">
      {/* Header - Fixed */}
      <div className="content-header flex-shrink-0">
        <h2 className="hierarchy-secondary">
          Local Businesses
        </h2>
        <p className="hierarchy-caption">
          Supporting our community
        </p>
      </div>

      {/* Scroll Indicator - Top */}
      {showScrollTop && (
        <div className="absolute top-20 left-1/2 transform -translate-x-1/2 z-10">
          <Button
            variant="ghost"
            size="sm"
            onClick={scrollToTop}
            className="h-8 w-8 p-0 bg-background/90 shadow-soft hover:bg-background rounded-full focus-visible-magazine interactive-button"
          >
            <ChevronUp className="w-4 h-4 text-muted-foreground" />
          </Button>
        </div>
      )}

      {/* Scrollable Content Area - Optimized bottom padding to reduce white space */}
      <div className="flex-1 overflow-hidden relative pb-20">
        <div
          ref={scrollContainerRef}
          className="h-full overflow-y-auto scrollbar-thin sidebar-scroll pr-2"
          onScroll={handleScroll}
        >
          <div className="space-y-3 pb-1">
            {advertisements.map((ad) => (
          <Card key={ad.id} className="interactive-card group">
            <CardHeader className="pb-2">
              <CardTitle className="emphasis-medium text-body-sm group-hover:text-magazine smooth-transition">
                {ad.title}
              </CardTitle>
            </CardHeader>
            <CardContent className="pt-0 flow-tight">
              <p className="hierarchy-caption mb-2">
                {ad.description}
              </p>
              
              {ad.contact && (
                <div className="space-y-2">
                  {ad.contact.phone && (
                    <div className="flex items-center text-xs text-muted-foreground">
                      <Phone className="w-3 h-3 mr-2 text-magazine" />
                      <span>{ad.contact.phone}</span>
                    </div>
                  )}

                  {ad.contact.email && (
                    <div className="flex items-center text-xs text-muted-foreground">
                      <Mail className="w-3 h-3 mr-2 text-magazine" />
                      <span className="truncate">{ad.contact.email}</span>
                    </div>
                  )}

                  {ad.contact.website && (
                    <div className="flex items-center text-xs text-muted-foreground">
                      <ExternalLink className="w-3 h-3 mr-2 text-magazine" />
                      <a
                        href={`https://${ad.contact.website}`}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="interactive-link truncate"
                      >
                        {ad.contact.website}
                      </a>
                    </div>
                  )}

                  {ad.contact.address && (
                    <div className="flex items-start text-xs text-muted-foreground">
                      <MapPin className="w-3 h-3 mr-2 mt-0.5 text-magazine flex-shrink-0" />
                      <span className="leading-tight">{ad.contact.address}</span>
                    </div>
                  )}
                </div>
              )}
            </CardContent>
          </Card>
        ))}
          </div>
        </div>
      </div>

      {/* Scroll Indicator - Bottom */}
      {showScrollBottom && (
        <div className="absolute bottom-24 left-1/2 transform -translate-x-1/2 z-10">
          <Button
            variant="ghost"
            size="sm"
            onClick={scrollToBottom}
            className="h-8 w-8 p-0 bg-background/90 shadow-soft hover:bg-background rounded-full focus-visible-magazine interactive-button"
          >
            <ChevronDown className="w-4 h-4 text-muted-foreground" />
          </Button>
        </div>
      )}

      {/* Call to Action - Fixed at bottom with optimized margin */}
      <div className="flex-shrink-0 mt-1 relative z-50">
        <Card className="bg-magazine-light border-magazine shadow-magazine">
          <CardContent className="p-4 text-center flow-tight">
            <h3 className="emphasis-high text-sm">
              Advertise Here
            </h3>
            <p className="hierarchy-caption text-xs">
              Reach the Lubenham community with your business
            </p>
            <Button
              size="sm"
              className="btn-magazine-primary text-xs interactive-button"
              onClick={() => window.location.href = 'mailto:<EMAIL>'}
            >
              Contact Us
            </Button>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};
