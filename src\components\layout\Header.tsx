import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Search, BookOpen, Menu, X } from 'lucide-react';
import { magazineInfo } from '@/data/magazineData';
import { SearchModal } from '@/components/magazine/SearchModal';

interface HeaderProps {
  onNavigateToContents?: () => void;
  onNavigateToPages?: () => void;
  onToggleSearch?: () => void;
}

export const Header: React.FC<HeaderProps> = ({
  onNavigateToContents,
  onNavigateToPages,
  onToggleSearch,
}) => {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [showSearch, setShowSearch] = useState(false);

  const scrollToTop = () => {
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  const scrollToContents = () => {
    document.getElementById('contents')?.scrollIntoView({ behavior: 'smooth' });
    setIsMobileMenuOpen(false);
  };

  const scrollToPages = () => {
    document.getElementById('pages')?.scrollIntoView({ behavior: 'smooth' });
    setIsMobileMenuOpen(false);
  };

  const handleSearch = () => {
    setShowSearch(true);
    setIsMobileMenuOpen(false);
  };

  const navigateToPage = (pageId: number) => {
    document.getElementById(`page-${pageId}`)?.scrollIntoView({ behavior: 'smooth' });
  };

  return (
    <>
      <header className="fixed top-0 left-0 right-0 z-50 glass-dark border-b border-subtle shadow-lg">
      <div className="container-magazine px-responsive py-3">
        <div className="flex items-center justify-between">
          {/* Logo/Title */}
          <button
            className="flex items-center space-x-3 cursor-pointer focus-magazine rounded-lg p-2 -m-2"
            onClick={scrollToTop}
            aria-label="Go to top of page"
          >
            <div className="w-8 h-8 bg-magazine rounded-lg flex items-center justify-center">
              <BookOpen className="w-5 h-5 text-white" />
            </div>
            <div>
              <h1 className="text-xl 2xl:text-2xl 3xl:text-3xl font-bold text-primary drop-shadow-sm">
                {magazineInfo.title}
              </h1>
              <p className="text-sm 2xl:text-base 3xl:text-lg text-secondary hidden sm:block drop-shadow-sm">
                {magazineInfo.subtitle}
              </p>
            </div>
          </button>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex items-center space-x-2 2xl:space-x-4 3xl:space-x-6">
            <Button
              variant="ghost"
              size="sm"
              onClick={scrollToContents}
              className="btn-magazine-ghost 2xl:text-base 3xl:text-lg 2xl:px-4 3xl:px-6"
            >
              <Menu className="w-4 h-4 2xl:w-5 2xl:h-5 3xl:w-6 3xl:h-6 mr-2" />
              Contents
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={scrollToPages}
              className="btn-magazine-ghost 2xl:text-base 3xl:text-lg 2xl:px-4 3xl:px-6"
            >
              <BookOpen className="w-4 h-4 2xl:w-5 2xl:h-5 3xl:w-6 3xl:h-6 mr-2" />
              Pages
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={handleSearch}
              className="btn-magazine-ghost 2xl:text-base 3xl:text-lg 2xl:px-4 3xl:px-6"
            >
              <Search className="w-4 h-4 2xl:w-5 2xl:h-5 3xl:w-6 3xl:h-6 mr-2" />
              Search
            </Button>
          </nav>

          {/* Mobile Menu Button */}
          <Button
            variant="ghost"
            size="sm"
            className="md:hidden btn-magazine-ghost"
            onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
            aria-label={isMobileMenuOpen ? "Close menu" : "Open menu"}
            aria-expanded={isMobileMenuOpen}
          >
            {isMobileMenuOpen ? (
              <X className="w-5 h-5" />
            ) : (
              <Menu className="w-5 h-5" />
            )}
          </Button>
        </div>

        {/* Mobile Navigation */}
        {isMobileMenuOpen && (
          <nav className="md:hidden mt-4 pb-4 border-t border-subtle pt-4 bg-surface/95">
            <div className="flex flex-col space-y-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={scrollToContents}
                className="justify-start btn-magazine-ghost"
              >
                <Menu className="w-4 h-4 mr-2" />
                Contents
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={scrollToPages}
                className="justify-start btn-magazine-ghost"
              >
                <BookOpen className="w-4 h-4 mr-2" />
                Pages
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={handleSearch}
                className="justify-start btn-magazine-ghost"
              >
                <Search className="w-4 h-4 mr-2" />
                Search
              </Button>
            </div>
          </nav>
        )}
      </div>
    </header>

    <SearchModal
      isOpen={showSearch}
      onClose={() => setShowSearch(false)}
      onNavigateToPage={navigateToPage}
    />
  </>
  );
};
