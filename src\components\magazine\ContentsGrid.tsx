import React from 'react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { ArrowUp, Eye, Clock } from 'lucide-react';
import { magazinePages } from '@/data/magazineData';

interface ContentsGridProps {
  onNavigateToPage: (pageId: number) => void;
  onScrollToTop: () => void;
}

export const ContentsGrid: React.FC<ContentsGridProps> = ({
  onNavigateToPage,
  onScrollToTop,
}) => {
  return (
    <section className="spacing-container spacing-section">
      <div className="container-wide">
        <div className="content-header">
          <h2 className="hierarchy-primary">
            Contents
          </h2>
          <p className="hierarchy-body max-w-2xl 2xl:max-w-3xl 3xl:max-w-4xl mx-auto">
            Browse through all pages of this issue. Click any page to read the full content.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 2xl:grid-cols-4 3xl:grid-cols-5 4xl:grid-cols-6 content-grid">
          {magazinePages.map((page) => (
            <Card
              key={page.id}
              className="group interactive-card border-0 glass-card hover-lift focus-visible-magazine"
              onClick={() => onNavigateToPage(page.id)}
              tabIndex={0}
              role="button"
              onKeyDown={(e) => {
                if (e.key === 'Enter' || e.key === ' ') {
                  e.preventDefault();
                  onNavigateToPage(page.id);
                }
              }}
            >
              <CardContent className="p-0">
                <div className="relative overflow-hidden rounded-t-lg">
                  <img
                    src={page.imageUrl}
                    alt={page.altText}
                    className="w-full h-48 object-cover transition-transform duration-300 group-hover:scale-110"
                    loading="lazy"
                  />
                  <div className="absolute top-3 left-3">
                    <Badge
                      variant="secondary"
                      className="bg-white/90 text-magazine font-semibold"
                    >
                      Page {page.id}
                    </Badge>
                  </div>
                  <div className="absolute top-3 right-3 opacity-0 group-hover:opacity-100 smooth-transition">
                    <Button size="sm" variant="secondary" className="h-8 w-8 p-0 interactive-button">
                      <Eye className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
                
                <div className="p-4 flow-tight">
                  <h3 className="emphasis-medium text-sm line-clamp-2 group-hover:text-magazine smooth-transition">
                    {page.title}
                  </h3>
                  <p className="hierarchy-caption text-xs line-clamp-3">
                    {page.summary}
                  </p>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        <div className="text-center">
          <Button
            onClick={onScrollToTop}
            variant="outline"
            className="border-magazine text-magazine hover:bg-magazine hover:text-white"
          >
            <ArrowUp className="w-4 h-4 mr-2" />
            Back to Top
          </Button>
        </div>
      </div>
    </section>
  );
};