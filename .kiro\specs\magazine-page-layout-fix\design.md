# Design Document

## Overview

This design addresses the layout rendering issues affecting magazine pages 10-16 in the digital magazine viewer. The solution focuses on fixing CSS constraints, text overflow handling, and ensuring consistent page rendering across all magazine pages.

## Architecture

The magazine page rendering system consists of:
- **PageViewer Component**: Main container that renders all magazine pages
- **MagazinePage Component**: Individual page renderer with image, text, and navigation
- **Magazine Data**: Static data containing page content and metadata
- **CSS Styling**: Tailwind classes and custom styles for layout control

## Root Cause Analysis

Based on the broken layout in pages 10-16, the likely causes are:
1. **Text Overflow**: Long OCR text content breaking container bounds
2. **Image Sizing**: Inconsistent image dimensions causing layout shifts
3. **CSS Constraints**: Missing or inadequate container constraints
4. **Collapsible Content**: Text version expansion affecting page layout
5. **Special Characters**: OCR text containing characters that break HTML rendering

## Components and Interfaces

### PageViewer Component Updates
```typescript
interface PageViewerProps {
  onScrollToTop: () => void;
}

// Enhanced with error boundaries and layout constraints
```

### MagazinePage Component Updates
```typescript
interface MagazinePageProps {
  page: MagazinePage;
  isLast: boolean;
  onScrollToTop: () => void;
}

// Enhanced with:
// - Text sanitization
// - Container constraints
// - Error handling
// - Performance optimizations
```

### CSS Architecture
```css
/* Container constraints */
.magazine-page-container {
  max-width: 100%;
  overflow: hidden;
}

/* Image constraints */
.magazine-page-image {
  max-width: 900px;
  height: auto;
  object-fit: contain;
}

/* Text content constraints */
.magazine-page-text {
  word-wrap: break-word;
  overflow-wrap: break-word;
  max-width: 100%;
}
```

## Data Models

### Enhanced MagazinePage Interface
```typescript
interface MagazinePage {
  id: number;
  title: string;
  summary: string;
  imageUrl: string;
  altText: string;
  ocrText: string;
  isPlaceholder?: boolean;
  // New fields for layout control
  hasLongContent?: boolean;
  textLength?: number;
  sanitizedText?: string;
}
```

## Error Handling

### Text Sanitization
- Remove or escape problematic HTML characters
- Truncate extremely long text blocks
- Handle special Unicode characters
- Implement fallback content for corrupted text

### Layout Error Recovery
- Implement error boundaries around individual pages
- Provide fallback rendering for broken pages
- Log layout errors for debugging
- Graceful degradation for missing images

### Performance Safeguards
- Lazy loading for page images
- Virtual scrolling for large content
- Debounced text rendering
- Memory cleanup for unmounted pages

## Testing Strategy

### Unit Tests
- Text sanitization functions
- Layout constraint calculations
- Image loading error handling
- Navigation functionality

### Integration Tests
- Full page rendering pipeline
- Cross-page navigation
- Responsive layout behavior
- Performance under load

### Visual Regression Tests
- Screenshot comparison for all pages
- Layout consistency across screen sizes
- Text overflow handling
- Collapsible content behavior

### Manual Testing Checklist
- [ ] All pages 1-16 render correctly
- [ ] Text versions expand without breaking layout
- [ ] Navigation works smoothly between pages
- [ ] Responsive behavior on mobile/tablet
- [ ] Performance is acceptable on slower devices

## Implementation Approach

### Phase 1: Immediate Fixes
1. Add container constraints to prevent overflow
2. Sanitize OCR text to remove problematic characters
3. Implement proper image sizing constraints
4. Fix collapsible content layout issues

### Phase 2: Enhanced Robustness
1. Add error boundaries for individual pages
2. Implement text truncation for extremely long content
3. Add loading states and error fallbacks
4. Optimize performance with lazy loading

### Phase 3: Polish and Testing
1. Comprehensive testing across all pages
2. Visual regression testing
3. Performance optimization
4. Accessibility improvements

## Technical Decisions

### CSS Strategy
- Use Tailwind utility classes for consistency
- Add custom CSS only where necessary
- Implement container queries for responsive behavior
- Use CSS Grid for reliable layout structure

### Text Handling
- Sanitize HTML entities in OCR text
- Implement text truncation with "show more" functionality
- Use CSS text-overflow for long titles
- Handle special characters gracefully

### Performance Optimization
- Implement intersection observer for lazy loading
- Use React.memo for page components
- Debounce text rendering for large content
- Optimize image loading with proper sizing

### Error Recovery
- Implement error boundaries at page level
- Provide meaningful error messages
- Log errors for debugging
- Graceful fallback to basic text rendering