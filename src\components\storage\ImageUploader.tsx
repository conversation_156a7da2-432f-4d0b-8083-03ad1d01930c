import React, { useState, useRef } from 'react';
import { useSupabaseUpload, useSupabaseStorageList } from '../../hooks/useSupabaseStorage';
import { Button } from '../ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Alert, AlertDescription } from '../ui/alert';
import { Progress } from '../ui/progress';
import { Input } from '../ui/input';
import { Loader2, Upload, X, Eye } from 'lucide-react';

interface ImageUploaderProps {
  bucket?: string;
  onUploadComplete?: (urls: string[]) => void;
}

export function ImageUploader({ bucket = 'agosto25', onUploadComplete }: ImageUploaderProps) {
  const [selectedFiles, setSelectedFiles] = useState<File[]>([]);
  const [uploadedUrls, setUploadedUrls] = useState<string[]>([]);
  const fileInputRef = useRef<HTMLInputElement>(null);
  
  const { upload, uploading, error, progress } = useSupabaseUpload();
  const { files, loading, listFiles, deleteFile } = useSupabaseStorageList(bucket);

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || []);
    const imageFiles = files.filter(file => file.type.startsWith('image/'));
    setSelectedFiles(imageFiles);
  };

  const handleUpload = async () => {
    if (selectedFiles.length === 0) return;

    const urls: string[] = [];
    
    for (const file of selectedFiles) {
      // Generate a unique filename with timestamp
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const filename = `${timestamp}-${file.name}`;
      
      const url = await upload(file, filename, bucket);
      if (url) {
        urls.push(url);
      }
    }

    setUploadedUrls(urls);
    setSelectedFiles([]);
    
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }

    if (onUploadComplete) {
      onUploadComplete(urls);
    }

    // Refresh file list
    await listFiles();
  };

  const removeSelectedFile = (index: number) => {
    setSelectedFiles(prev => prev.filter((_, i) => i !== index));
  };

  const handleBulkUpload = async () => {
    // Predefined image paths from your request
    const imagePaths = [
      "C:\\Users\\<USER>\\Documents\\img20250804_11243848.jpg",
      "C:\\Users\\<USER>\\Documents\\img20250804_11265445.jpg",
      "C:\\Users\\<USER>\\Documents\\img20250804_11300092.jpg",
      "C:\\Users\\<USER>\\Documents\\img20250804_11322270.jpg",
      "C:\\Users\\<USER>\\Documents\\img20250804_11343353.jpg",
      "C:\\Users\\<USER>\\Documents\\img20250804_11364245.jpg",
      "C:\\Users\\<USER>\\Documents\\img20250804_11403233.jpg",
      "C:\\Users\\<USER>\\Documents\\img20250804_11513387.jpg",
      "C:\\Users\\<USER>\\Documents\\img20250804_11532847.jpg",
      "C:\\Users\\<USER>\\Documents\\img20250804_11552139.jpg",
      "C:\\Users\\<USER>\\Documents\\img20250804_12211122.jpg",
      "C:\\Users\\<USER>\\Documents\\img20250804_12233061.jpg",
      "C:\\Users\\<USER>\\Documents\\img20250804_12242970.jpg",
      "C:\\Users\\<USER>\\Documents\\img20250804_12253712.jpg",
      "C:\\Users\\<USER>\\Documents\\img20250804_12273075.jpg",
      "C:\\Users\\<USER>\\Documents\\img20250804_12283724.jpg"
    ];

    // Note: This is a demonstration. In a real web app, you can't access local file paths directly.
    // You would need to use a file input or drag-and-drop interface.
    alert('Para fazer upload das imagens locais, use o seletor de arquivos abaixo ou arraste as imagens para a área de upload.');
  };

  React.useEffect(() => {
    listFiles();
  }, []);

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Upload className="w-5 h-5" />
            Upload de Imagens para o Supabase Storage
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {error && (
            <Alert variant="destructive">
              <AlertDescription>
                Erro no upload: {error.message}
              </AlertDescription>
            </Alert>
          )}

          <div className="space-y-4">
            <div>
              <Input
                ref={fileInputRef}
                type="file"
                multiple
                accept="image/*"
                onChange={handleFileSelect}
                className="cursor-pointer"
              />
              <p className="text-sm text-muted-foreground mt-2">
                Selecione uma ou mais imagens para fazer upload
              </p>
            </div>

            {selectedFiles.length > 0 && (
              <div className="space-y-2">
                <h4 className="font-medium">Arquivos selecionados:</h4>
                <div className="space-y-2">
                  {selectedFiles.map((file, index) => (
                    <div key={index} className="flex items-center justify-between p-2 border rounded">
                      <span className="text-sm">{file.name}</span>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => removeSelectedFile(index)}
                      >
                        <X className="w-4 h-4" />
                      </Button>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {uploading && (
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <Loader2 className="w-4 h-4 animate-spin" />
                  <span>Fazendo upload...</span>
                </div>
                <Progress value={progress} className="w-full" />
              </div>
            )}

            <div className="flex gap-2">
              <Button
                onClick={handleUpload}
                disabled={selectedFiles.length === 0 || uploading}
                className="flex-1"
              >
                {uploading ? (
                  <>
                    <Loader2 className="w-4 h-4 animate-spin mr-2" />
                    Enviando...
                  </>
                ) : (
                  <>
                    <Upload className="w-4 h-4 mr-2" />
                    Upload ({selectedFiles.length} arquivo{selectedFiles.length !== 1 ? 's' : ''})
                  </>
                )}
              </Button>
              
              <Button
                variant="outline"
                onClick={handleBulkUpload}
                disabled={uploading}
              >
                Upload das 16 Imagens
              </Button>
            </div>
          </div>

          {uploadedUrls.length > 0 && (
            <div className="space-y-2">
              <h4 className="font-medium text-green-600">Upload concluído!</h4>
              <div className="space-y-1">
                {uploadedUrls.map((url, index) => (
                  <div key={index} className="text-sm p-2 bg-green-50 rounded">
                    <a href={url} target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline">
                      {url}
                    </a>
                  </div>
                ))}
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Arquivos no Bucket "{bucket}"</CardTitle>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex items-center justify-center p-4">
              <Loader2 className="w-6 h-6 animate-spin" />
              <span className="ml-2">Carregando arquivos...</span>
            </div>
          ) : (
            <div className="space-y-2">
              {files.length === 0 ? (
                <p className="text-muted-foreground">Nenhum arquivo encontrado no bucket.</p>
              ) : (
                files.map((file, index) => (
                  <div key={index} className="flex items-center justify-between p-2 border rounded">
                    <div className="flex items-center gap-2">
                      <Eye className="w-4 h-4" />
                      <span className="text-sm">{file.name}</span>
                      <span className="text-xs text-muted-foreground">
                        ({(file.metadata?.size / 1024 / 1024).toFixed(2)} MB)
                      </span>
                    </div>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => deleteFile(file.name)}
                      className="text-red-600 hover:text-red-800"
                    >
                      <X className="w-4 h-4" />
                    </Button>
                  </div>
                ))
              )}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}