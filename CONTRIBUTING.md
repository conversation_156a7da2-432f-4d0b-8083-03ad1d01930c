# 🤝 Contribuindo para Lubenham News

Obrigado por considerar contribuir para o projeto Lubenham News! Este documento fornece diretrizes para contribuições.

## 📋 Código de Conduta

Este projeto segue um código de conduta. Ao participar, você concorda em manter um ambiente respeitoso e inclusivo.

## 🚀 Como Contribuir

### Reportando Bugs

1. **Verifique** se o bug já foi reportado nas [Issues](../../issues)
2. **Crie uma nova issue** com:
   - Título claro e descritivo
   - Passos para reproduzir o problema
   - Comportamento esperado vs atual
   - Screenshots (se aplicável)
   - Informações do ambiente (browser, OS, etc.)

### Sugerindo Melhorias

1. **Abra uma issue** com a tag `enhancement`
2. **Descreva** a melhoria proposta
3. **Explique** por que seria útil para a comunidade
4. **Forneça** exemplos de uso, se possível

### Contribuindo com Código

#### Configuração do Ambiente

```bash
# 1. Fork o repositório
# 2. Clone seu fork
git clone https://github.com/seu-usuario/lubenham-news.git
cd lubenham-news

# 3. Instale dependências
npm install

# 4. Configure variáveis de ambiente
cp .env.example .env
# Edite .env com suas configurações

# 5. Inicie o servidor de desenvolvimento
npm run dev
```

#### Processo de Desenvolvimento

1. **Crie uma branch** para sua feature:
   ```bash
   git checkout -b feature/nome-da-feature
   ```

2. **Faça suas alterações** seguindo os padrões:
   - Use TypeScript para tipagem
   - Siga as convenções de nomenclatura
   - Adicione comentários quando necessário
   - Mantenha componentes pequenos e focados

3. **Teste suas alterações**:
   ```bash
   npm run lint
   npm run build
   ```

4. **Commit suas mudanças**:
   ```bash
   git add .
   git commit -m "feat: adiciona nova funcionalidade X"
   ```

5. **Push para sua branch**:
   ```bash
   git push origin feature/nome-da-feature
   ```

6. **Abra um Pull Request**

## 📝 Padrões de Código

### Estrutura de Arquivos

```
src/
├── components/
│   ├── magazine/     # Componentes específicos da revista
│   ├── ui/          # Componentes base (shadcn/ui)
│   └── storage/     # Componentes de upload/storage
├── hooks/           # Hooks personalizados
├── data/           # Dados estáticos
├── types/          # Definições TypeScript
└── lib/            # Utilitários
```

### Convenções de Nomenclatura

- **Componentes**: PascalCase (`MagazineHero.tsx`)
- **Hooks**: camelCase com prefixo `use` (`useSupabase.ts`)
- **Utilitários**: camelCase (`formatDate.ts`)
- **Constantes**: UPPER_SNAKE_CASE (`API_ENDPOINTS`)

### Padrões de Commit

Use [Conventional Commits](https://www.conventionalcommits.org/):

- `feat:` nova funcionalidade
- `fix:` correção de bug
- `docs:` documentação
- `style:` formatação, sem mudança de lógica
- `refactor:` refatoração de código
- `test:` adição/correção de testes
- `chore:` tarefas de manutenção

### Componentes React

```typescript
// Exemplo de componente bem estruturado
import React from 'react';
import { cn } from '@/lib/utils';

interface ComponentProps {
  title: string;
  description?: string;
  className?: string;
}

export const Component: React.FC<ComponentProps> = ({
  title,
  description,
  className
}) => {
  return (
    <div className={cn("base-classes", className)}>
      <h2>{title}</h2>
      {description && <p>{description}</p>}
    </div>
  );
};
```

## 🎨 Design System

### Cores Principais
- **Verde**: `#22c55e` (primary)
- **Verde Escuro**: `#16a34a` (primary-dark)
- **Creme**: `#fef3c7` (accent)
- **Cinza**: `#6b7280` (muted)

### Tipografia
- **Títulos**: Inter, system-ui
- **Corpo**: Inter, system-ui
- **Código**: JetBrains Mono, monospace

## 📱 Responsividade

Teste em diferentes tamanhos:
- **Mobile**: 320px - 768px
- **Tablet**: 768px - 1024px
- **Desktop**: 1024px+

## ♿ Acessibilidade

- Use textos alternativos em imagens
- Mantenha contraste adequado
- Suporte navegação por teclado
- Use ARIA labels quando necessário

## 🧪 Testes

```bash
# Lint
npm run lint

# Build test
npm run build

# Preview build
npm run preview
```

## 📚 Recursos Úteis

- [React Documentation](https://react.dev/)
- [TypeScript Handbook](https://www.typescriptlang.org/docs/)
- [Tailwind CSS](https://tailwindcss.com/docs)
- [Radix UI](https://www.radix-ui.com/)
- [Supabase Docs](https://supabase.com/docs)

## ❓ Dúvidas?

- Abra uma [Discussion](../../discussions)
- Entre em contato com a equipe
- Consulte a documentação existente

---

**Obrigado por contribuir com a Lubenham News! 🌟**