import React from 'react';
import { Head<PERSON> } from './Header';
import { Sidebar } from './Sidebar';
import { FooterBanner } from './FooterBanner';

interface MainLayoutProps {
  children: React.ReactNode;
}

export const MainLayout: React.FC<MainLayoutProps> = ({ children }) => {
  return (
    <div className="min-h-screen flex flex-col bg-background">
      {/* Fixed Header */}
      <Header />

      {/* Main Content Area */}
      <div className="flex-1 pt-16 pb-24 sm:pb-28"> {/* pt-16 for fixed header, pb-24/28 for compact fixed footer with extra space */}
        <div className="container-magazine spacing-container spacing-section">
          <div className="flex flex-col xl:flex-row spacing-grid">
            {/* Main Content - Optimized for Large Screens */}
            <main className="flex-1 max-w-5xl xl:max-w-6xl 2xl:max-w-7xl 3xl:max-w-8xl">
              <div className="card-magazine overflow-hidden">
                {children}
              </div>
            </main>

            {/* Right Sidebar for Advertisements - Responsive Width */}
            <aside className="hidden xl:block w-80 2xl:w-96 3xl:w-[28rem] flex-shrink-0">
              <div className="sticky top-24 h-[calc(100vh-12rem)] card-magazine p-responsive">
                <Sidebar />
              </div>
            </aside>
          </div>

          {/* Mobile Sidebar - Show below main content on smaller screens */}
          <div className="xl:hidden mt-responsive mb-responsive">
            <div className="card-magazine p-responsive">
              <Sidebar />
            </div>
          </div>
        </div>
      </div>

      {/* Fixed Footer Banner */}
      <FooterBanner />
    </div>
  );
};
