import { useState, useMemo } from 'react';
import MiniSearch from 'minisearch';
import { magazinePages, MagazinePage } from '@/data/magazineData';

export interface SearchResult {
  id: number;
  title: string;
  summary: string;
  match: string;
  score: number;
}

export const useSearch = () => {
  const [searchQuery, setSearchQuery] = useState('');

  const miniSearch = useMemo(() => {
    const ms = new MiniSearch({
      fields: ['title', 'summary', 'ocrText'],
      storeFields: ['id', 'title', 'summary'],
      searchOptions: {
        boost: { title: 2, summary: 1.5 },
        fuzzy: 0.2,
        prefix: true,
      },
    });

    // Add documents to the search index
    const searchableData = magazinePages
      .map(page => ({
        id: page.id,
        title: page.title,
        summary: page.summary,
        ocrText: page.ocrText,
      }));

    ms.addAll(searchableData);
    return ms;
  }, []);

  const searchResults = useMemo(() => {
    if (!searchQuery.trim()) return [];

    try {
      const results = miniSearch.search(searchQuery);

      return results.map((result: any) => ({
        id: result.id,
        title: result.title,
        summary: result.summary,
        match: result.match || searchQuery,
        score: result.score,
      })) as SearchResult[];
    } catch (error) {
      console.warn('Search error:', error);
      return [];
    }
  }, [searchQuery, miniSearch]);

  return {
    searchQuery,
    setSearchQuery,
    searchResults,
    isSearching: searchQuery.trim().length > 0,
  };
};