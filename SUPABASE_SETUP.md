# Configuração do Supabase

Este projeto está configurado para usar o Supabase como banco de dados. Siga as instruções abaixo para configurar o ambiente.

## Configuração do Ambiente

### 1. Arquivo .env
O arquivo `.env` já foi criado com as seguintes variáveis:

```env
VITE_SUPABASE_URL=https://vxcwvzrdzlvnsvudwfnr.supabase.co
VITE_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
DATABASE_PASSWORD=P+-@@6CUDUJSUpy
```

### 2. MCP (Model Context Protocol)
O MCP foi configurado em `.kiro/settings/mcp.json` para permitir interação com o Supabase via AI.

Token de acesso: `********************************************`

## Configuração do Banco de Dados

### 1. Executar Schema SQL
Execute o arquivo `database/schema.sql` no SQL Editor do Supabase Dashboard:

1. Acesse: https://supabase.com/dashboard/project/vxcwvzrdzlvnsvudwfnr/sql
2. Cole o conteúdo do arquivo `database/schema.sql`
3. Execute o script

### 2. Configurar Políticas de Segurança
As políticas RLS (Row Level Security) já estão incluídas no schema, mas você pode ajustá-las conforme necessário.

## Estrutura do Projeto

### Arquivos Criados:
- `src/lib/supabase.ts` - Cliente Supabase configurado
- `src/hooks/useSupabase.ts` - Hooks personalizados para operações CRUD
- `src/types/database.ts` - Tipos TypeScript para o banco
- `src/components/database/DatabaseExample.tsx` - Componente de exemplo
- `database/schema.sql` - Schema do banco de dados

### Como Usar:

```tsx
import { useSupabaseQuery } from '../hooks/useSupabase';

function MyComponent() {
  const { data, loading, error } = useSupabaseQuery('todos');
  
  if (loading) return <div>Carregando...</div>;
  if (error) return <div>Erro: {error.message}</div>;
  
  return (
    <div>
      {data?.map(item => (
        <div key={item.id}>{item.title}</div>
      ))}
    </div>
  );
}
```

## Produção

Para produção, configure as variáveis de ambiente no Supabase Dashboard:
https://supabase.com/dashboard/project/vxcwvzrdzlvnsvudwfnr/functions/secrets

## Comandos Úteis

```bash
# Instalar dependências (já instalado)
npm install @supabase/supabase-js

# Executar em desenvolvimento
npm run dev

# Build para produção
npm run build
```

## Recursos Disponíveis

- ✅ Cliente Supabase configurado
- ✅ Hooks personalizados para CRUD
- ✅ Tipos TypeScript
- ✅ Componente de exemplo
- ✅ Schema do banco
- ✅ MCP configurado
- ✅ Políticas de segurança RLS

## Links Úteis

- [Dashboard do Projeto](https://supabase.com/dashboard/project/vxcwvzrdzlvnsvudwfnr)
- [SQL Editor](https://supabase.com/dashboard/project/vxcwvzrdzlvnsvudwfnr/sql)
- [Documentação Supabase](https://supabase.com/docs)