import { createClient } from '@supabase/supabase-js';
import type { Database } from '../types/database';

const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
const supabaseKey = import.meta.env.VITE_SUPABASE_ANON_KEY;

// Configuração mais robusta para desenvolvimento
const isDevelopment = import.meta.env.DEV;

if (!supabaseUrl || !supabaseKey) {
  if (isDevelopment) {
    console.warn('⚠️ Supabase environment variables not found. Some features may not work.');
    console.log('📝 Create a .env file with VITE_SUPABASE_URL and VITE_SUPABASE_ANON_KEY');
  } else {
    throw new Error('Missing Supabase environment variables');
  }
}

// Criar cliente com configurações otimizadas
export const supabase = createClient<Database>(
  supabaseUrl || 'https://placeholder.supabase.co',
  supabaseKey || 'placeholder-key',
  {
    auth: {
      autoRefreshToken: true,
      persistSession: true,
      detectSessionInUrl: true
    },
    realtime: {
      params: {
        eventsPerSecond: 10
      }
    }
  }
);

// Função para verificar se o Supabase está configurado
export const isSupabaseConfigured = (): boolean => {
  return !!(supabaseUrl && supabaseKey && 
    supabaseUrl !== 'https://placeholder.supabase.co' && 
    supabaseKey !== 'placeholder-key');
};

// Log de status em desenvolvimento
if (isDevelopment) {
  if (isSupabaseConfigured()) {
    console.log('✅ Supabase configured successfully');
  } else {
    console.log('⚠️ Supabase not configured - running in offline mode');
  }
}

export default supabase;