import React from 'react';
import { MagazineHero } from './MagazineHero';
import { ContentsGrid } from './ContentsGrid';
import { PageViewer } from './PageViewer';

export const Magazine: React.FC = () => {

  const scrollToTop = () => {
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  const scrollToContents = () => {
    document.getElementById('contents')?.scrollIntoView({ behavior: 'smooth' });
  };

  const scrollToPages = () => {
    document.getElementById('pages')?.scrollIntoView({ behavior: 'smooth' });
  };

  const navigateToPage = (pageId: number) => {
    document.getElementById(`page-${pageId}`)?.scrollIntoView({ behavior: 'smooth' });
  };

  return (
    <div className="min-h-screen">
      {/* Hero Section - Full Width */}
      <MagazineHero
        onNavigateToContents={scrollToContents}
        onNavigateToPages={scrollToPages}
        onToggleSearch={() => {}}
      />

      {/* Contents Section - Contained */}
      <div id="contents" className="py-8">
        <ContentsGrid
          onNavigateToPage={navigateToPage}
          onScrollToTop={scrollToTop}
        />
      </div>

      {/* Pages Section - Contained */}
      <div id="pages" className="py-8">
        <PageViewer onScrollToTop={scrollToTop} />
      </div>


    </div>
  );
};