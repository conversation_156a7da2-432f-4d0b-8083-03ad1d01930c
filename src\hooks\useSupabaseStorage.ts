import { useState } from 'react';
import { supabase } from '../lib/supabase';
import type { StorageError } from '@supabase/supabase-js';

interface UseSupabaseUpload {
  upload: (file: File, path: string, bucket?: string) => Promise<string | null>;
  uploading: boolean;
  error: StorageError | null;
  progress: number;
}

export function useSupabaseUpload(): UseSupabaseUpload {
  const [uploading, setUploading] = useState(false);
  const [error, setError] = useState<StorageError | null>(null);
  const [progress, setProgress] = useState(0);

  const upload = async (file: File, path: string, bucket: string = 'agosto25'): Promise<string | null> => {
    try {
      setUploading(true);
      setError(null);
      setProgress(0);

      // Upload the file
      const { data, error: uploadError } = await supabase.storage
        .from(bucket)
        .upload(path, file, {
          cacheControl: '3600',
          upsert: false
        });

      if (uploadError) {
        setError(uploadError);
        return null;
      }

      setProgress(100);

      // Get the public URL
      const { data: { publicUrl } } = supabase.storage
        .from(bucket)
        .getPublicUrl(data.path);

      return publicUrl;
    } catch (err) {
      console.error('Error uploading file:', err);
      setError(err as StorageError);
      return null;
    } finally {
      setUploading(false);
    }
  };

  return {
    upload,
    uploading,
    error,
    progress
  };
}

export function useSupabaseStorageList(bucket: string = 'agosto25') {
  const [files, setFiles] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<StorageError | null>(null);

  const listFiles = async (folder: string = '') => {
    try {
      setLoading(true);
      setError(null);

      const { data, error: listError } = await supabase.storage
        .from(bucket)
        .list(folder, {
          limit: 100,
          offset: 0,
          sortBy: { column: 'name', order: 'asc' }
        });

      if (listError) {
        setError(listError);
        return;
      }

      setFiles(data || []);
    } catch (err) {
      console.error('Error listing files:', err);
      setError(err as StorageError);
    } finally {
      setLoading(false);
    }
  };

  const deleteFile = async (path: string) => {
    try {
      const { error: deleteError } = await supabase.storage
        .from(bucket)
        .remove([path]);

      if (deleteError) {
        setError(deleteError);
        return false;
      }

      // Refresh the file list
      await listFiles();
      return true;
    } catch (err) {
      console.error('Error deleting file:', err);
      setError(err as StorageError);
      return false;
    }
  };

  return {
    files,
    loading,
    error,
    listFiles,
    deleteFile
  };
}