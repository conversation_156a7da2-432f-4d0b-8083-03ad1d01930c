import React, { useState } from 'react';
import { <PERSON><PERSON>, Dialog<PERSON>ontent, Di<PERSON><PERSON>eader, DialogTitle } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Search, X, Mail } from 'lucide-react';
import { useSearch } from '@/hooks/useSearch';

interface SearchModalProps {
  isOpen: boolean;
  onClose: () => void;
  onNavigateToPage: (pageId: number) => void;
}

export const SearchModal: React.FC<SearchModalProps> = ({
  isOpen,
  onClose,
  onNavigateToPage,
}) => {
  const { searchQuery, setSearchQuery, searchResults, isSearching } = useSearch();

  const handleResultClick = (pageId: number) => {
    onNavigateToPage(pageId);
    onClose();
    setSearchQuery('');
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[80vh] overflow-hidden">
        <DialogHeader>
          <div className="flex items-center justify-between">
            <DialogTitle className="flex items-center gap-2">
              <Search className="w-5 h-5 text-magazine" />
              Search Lubenham News
            </DialogTitle>
            <Button
              variant="outline"
              size="sm"
              onClick={() => window.location.href = 'mailto:<EMAIL>'}
              className="text-xs h-8 px-3 border-magazine text-magazine hover:bg-magazine-light"
            >
              <Mail className="w-3 h-3 mr-1" />
              Advertise
            </Button>
          </div>
        </DialogHeader>
        
        <div className="space-y-4">
          <div className="relative">
            <Input
              placeholder="Search articles, events, or content..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
              autoFocus
            />
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
            {searchQuery && (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setSearchQuery('')}
                className="absolute right-2 top-1/2 transform -translate-y-1/2 h-6 w-6 p-0"
              >
                <X className="w-4 h-4" />
              </Button>
            )}
          </div>

          {/* Advertising prompt for empty search */}
          {!searchQuery.trim() && (
            <div className="text-center py-2 px-4 bg-magazine-light/30 rounded-lg border border-magazine/20">
              <p className="text-xs text-muted-foreground">
                Want to reach the Lubenham community?
                <button
                  onClick={() => window.location.href = 'mailto:<EMAIL>'}
                  className="ml-1 text-magazine hover:underline font-medium"
                >
                  Contact us about advertising
                </button>
              </p>
            </div>
          )}

          <div className="max-h-96 overflow-y-auto">
            {isSearching && searchResults.length === 0 && (
              <div className="text-center py-8 text-muted-foreground">
                <Search className="w-12 h-12 mx-auto mb-4 opacity-50" />
                <p>No results found for "{searchQuery}"</p>
                <p className="text-sm mt-2">Try searching for events, church, pub, or village activities</p>
              </div>
            )}

            {!isSearching && (
              <div className="text-center py-8 text-muted-foreground">
                <Search className="w-12 h-12 mx-auto mb-4 opacity-50" />
                <p>Start typing to search through the magazine</p>
                <p className="text-sm mt-2">Search articles, announcements, and local content</p>
              </div>
            )}

            {searchResults.map((result) => (
              <div
                key={result.id}
                onClick={() => handleResultClick(result.id)}
                className="p-4 rounded-lg border hover:bg-accent cursor-pointer transition-colors"
              >
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <h3 className="font-semibold text-sm mb-1">
                      Page {result.id}: {result.title}
                    </h3>
                    <p className="text-sm text-muted-foreground line-clamp-2">
                      {result.summary}
                    </p>
                  </div>
                  <div className="ml-4 text-xs text-muted-foreground">
                    Page {result.id}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};