import { clsx, type ClassValue } from "clsx"
import { twMerge } from "tailwind-merge"

// Re-exports das outras bibliotecas de utilitários
export * from './constants';
export * from './format';
export * from './validation';
export * from './performance';

/**
 * Combina classes CSS usando clsx e tailwind-merge
 */
export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

/**
 * Gera um ID único simples
 */
export const generateId = (): string => {
  return Math.random().toString(36).substr(2, 9);
};

/**
 * Copia texto para a área de transferência
 */
export const copyToClipboard = async (text: string): Promise<boolean> => {
  try {
    await navigator.clipboard.writeText(text);
    return true;
  } catch (error) {
    // Fallback para navegadores mais antigos
    const textArea = document.createElement('textarea');
    textArea.value = text;
    textArea.style.position = 'fixed';
    textArea.style.left = '-999999px';
    textArea.style.top = '-999999px';
    document.body.appendChild(textArea);
    textArea.focus();
    textArea.select();
    
    try {
      document.execCommand('copy');
      document.body.removeChild(textArea);
      return true;
    } catch (err) {
      document.body.removeChild(textArea);
      return false;
    }
  }
};

/**
 * Detecta se está rodando no servidor (SSR)
 */
export const isServer = typeof window === 'undefined';

/**
 * Detecta se está rodando no cliente
 */
export const isClient = !isServer;

/**
 * Obtém valor de uma variável CSS
 */
export const getCSSVariable = (variable: string): string => {
  if (isServer) return '';
  return getComputedStyle(document.documentElement)
    .getPropertyValue(variable)
    .trim();
};

/**
 * Define valor de uma variável CSS
 */
export const setCSSVariable = (variable: string, value: string): void => {
  if (isServer) return;
  document.documentElement.style.setProperty(variable, value);
};

/**
 * Converte rem para pixels
 */
export const remToPx = (rem: number): number => {
  if (isServer) return rem * 16; // Assume 16px como padrão
  return rem * parseFloat(getComputedStyle(document.documentElement).fontSize);
};

/**
 * Converte pixels para rem
 */
export const pxToRem = (px: number): number => {
  if (isServer) return px / 16; // Assume 16px como padrão
  return px / parseFloat(getComputedStyle(document.documentElement).fontSize);
};

/**
 * Obtém informações do dispositivo
 */
export const getDeviceInfo = () => {
  if (isServer) {
    return {
      isMobile: false,
      isTablet: false,
      isDesktop: true,
      userAgent: '',
      platform: 'server'
    };
  }

  const userAgent = navigator.userAgent;
  const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(userAgent);
  const isTablet = /iPad|Android(?!.*Mobile)/i.test(userAgent);
  const isDesktop = !isMobile && !isTablet;

  return {
    isMobile,
    isTablet,
    isDesktop,
    userAgent,
    platform: navigator.platform
  };
};

/**
 * Obtém parâmetros da URL
 */
export const getUrlParams = (): URLSearchParams => {
  if (isServer) return new URLSearchParams();
  return new URLSearchParams(window.location.search);
};

/**
 * Atualiza parâmetros da URL sem recarregar a página
 */
export const updateUrlParams = (params: Record<string, string>): void => {
  if (isServer) return;
  
  const url = new URL(window.location.href);
  
  Object.entries(params).forEach(([key, value]) => {
    if (value) {
      url.searchParams.set(key, value);
    } else {
      url.searchParams.delete(key);
    }
  });
  
  window.history.replaceState({}, '', url.toString());
};

/**
 * Obtém dimensões da viewport
 */
export const getViewportSize = () => {
  if (isServer) {
    return { width: 1920, height: 1080 };
  }

  return {
    width: window.innerWidth,
    height: window.innerHeight
  };
};

/**
 * Verifica se um elemento está visível na viewport
 */
export const isElementInViewport = (element: Element): boolean => {
  if (isServer) return false;
  
  const rect = element.getBoundingClientRect();
  const viewport = getViewportSize();
  
  return (
    rect.top >= 0 &&
    rect.left >= 0 &&
    rect.bottom <= viewport.height &&
    rect.right <= viewport.width
  );
};

/**
 * Aguarda um tempo específico
 */
export const sleep = (ms: number): Promise<void> => {
  return new Promise(resolve => setTimeout(resolve, ms));
};

/**
 * Executa função apenas uma vez
 */
export const once = <T extends (...args: any[]) => any>(fn: T): T => {
  let called = false;
  let result: ReturnType<T>;
  
  return ((...args: Parameters<T>) => {
    if (!called) {
      called = true;
      result = fn(...args);
    }
    return result;
  }) as T;
};
