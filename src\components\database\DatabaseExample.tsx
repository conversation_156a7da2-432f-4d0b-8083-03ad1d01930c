import React, { useState } from 'react';
import { useSupabaseQuery, useSupabaseInsert } from '../../hooks/useSupabase';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Alert, AlertDescription } from '../ui/alert';
import { Loader2 } from 'lucide-react';

interface Todo {
  id: number;
  title: string;
  completed: boolean;
  created_at: string;
}

export function DatabaseExample() {
  const [newTodo, setNewTodo] = useState('');
  const { data: todos, loading, error, refetch } = useSupabaseQuery<Todo>('todos');
  const { insert, loading: inserting, error: insertError } = useSupabaseInsert<Todo>('todos');

  const handleAddTodo = async () => {
    if (!newTodo.trim()) return;
    
    const result = await insert({
      title: newTodo,
      completed: false
    });
    
    if (result) {
      setNewTodo('');
      refetch();
    }
  };

  if (loading) {
    return (
      <Card className="w-full max-w-2xl mx-auto">
        <CardContent className="flex items-center justify-center p-6">
          <Loader2 className="h-6 w-6 animate-spin" />
          <span className="ml-2">Carregando dados...</span>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle>Exemplo de Integração com Supabase</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {error && (
          <Alert variant="destructive">
            <AlertDescription>
              Erro ao carregar dados: {error.message}
            </AlertDescription>
          </Alert>
        )}
        
        {insertError && (
          <Alert variant="destructive">
            <AlertDescription>
              Erro ao inserir dados: {insertError.message}
            </AlertDescription>
          </Alert>
        )}

        <div className="flex gap-2">
          <Input
            placeholder="Digite uma nova tarefa..."
            value={newTodo}
            onChange={(e) => setNewTodo(e.target.value)}
            onKeyPress={(e) => e.key === 'Enter' && handleAddTodo()}
          />
          <Button 
            onClick={handleAddTodo} 
            disabled={inserting || !newTodo.trim()}
          >
            {inserting ? (
              <>
                <Loader2 className="h-4 w-4 animate-spin mr-2" />
                Adicionando...
              </>
            ) : (
              'Adicionar'
            )}
          </Button>
        </div>

        <div className="space-y-2">
          <h3 className="font-semibold">Tarefas:</h3>
          {todos && todos.length > 0 ? (
            <ul className="space-y-2">
              {todos.map((todo) => (
                <li key={todo.id} className="p-2 border rounded">
                  <div className="flex items-center justify-between">
                    <span className={todo.completed ? 'line-through' : ''}>
                      {todo.title}
                    </span>
                    <span className="text-sm text-gray-500">
                      {new Date(todo.created_at).toLocaleDateString()}
                    </span>
                  </div>
                </li>
              ))}
            </ul>
          ) : (
            <p className="text-gray-500">Nenhuma tarefa encontrada.</p>
          )}
        </div>

        <Button onClick={refetch} variant="outline" className="w-full">
          Atualizar Lista
        </Button>
      </CardContent>
    </Card>
  );
}