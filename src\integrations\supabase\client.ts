// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://vxcwvzrdzlvnsvudwfnr.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InZ4Y3d2enJkemx2bnN2dWR3Zm5yIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTQzMDYwNjQsImV4cCI6MjA2OTg4MjA2NH0.ZA2jHCqCQs-2TQVSmHIdZzrIucpeTXOjyuXBzLqBsf4";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY, {
  auth: {
    storage: localStorage,
    persistSession: true,
    autoRefreshToken: true,
  }
});