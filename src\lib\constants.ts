// Lubenham News Application Constants

export const APP_CONFIG = {
  name: 'Lubenham News',
  description: 'Your Community Connection',
  version: '1.0.0',
  author: 'Lubenham News Team',
  url: 'https://lubenhamnews.com',
  email: '<EMAIL>'
} as const;

export const SUPABASE_CONFIG = {
  defaultBucket: 'agosto25',
  maxFileSize: 10 * 1024 * 1024, // 10MB
  allowedFileTypes: ['image/jpeg', 'image/png', 'image/webp', 'image/svg+xml'],
  cacheControl: '3600'
} as const;

export const UI_CONFIG = {
  breakpoints: {
    mobile: 768,
    tablet: 1024,
    desktop: 1280
  },
  animations: {
    duration: {
      fast: 150,
      normal: 300,
      slow: 500
    },
    easing: 'cubic-bezier(0.4, 0, 0.2, 1)'
  },
  colors: {
    primary: '#22c55e',
    primaryDark: '#16a34a',
    accent: '#fef3c7',
    muted: '#6b7280'
  }
} as const;

export const SEARCH_CONFIG = {
  minQueryLength: 2,
  maxResults: 50,
  searchFields: ['title', 'summary', 'ocrText'],
  boostFields: {
    title: 3,
    summary: 2,
    ocrText: 1
  }
} as const;

export const MAGAZINE_CONFIG = {
  totalPages: 16,
  lazyLoadOffset: 100,
  scrollBehavior: 'smooth' as ScrollBehavior,
  imageFormats: ['webp', 'jpg', 'png']
} as const;

export const META_TAGS = {
  keywords: [
    'Lubenham',
    'news',
    'community',
    'digital magazine',
    'local events',
    'Leicestershire',
    'United Kingdom',
    'community news',
    'local events'
  ],
  openGraph: {
    type: 'website',
    locale: 'en_GB',
    siteName: APP_CONFIG.name,
    imageWidth: 1200,
    imageHeight: 630
  },
  twitter: {
    card: 'summary_large_image',
    site: '@lubenhamnews'
  }
} as const;

export const API_ENDPOINTS = {
  supabase: {
    storage: '/storage/v1/object/public',
    rest: '/rest/v1'
  }
} as const;

// Regex patterns
export const PATTERNS = {
  email: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  phone: /^\+?[\d\s\-\(\)]+$/,
  url: /^https?:\/\/.+/
} as const;

// Error messages
export const ERROR_MESSAGES = {
  network: 'Connection error. Please check your internet.',
  upload: 'Error uploading file.',
  fileSize: `File too large. Maximum ${SUPABASE_CONFIG.maxFileSize / 1024 / 1024}MB.`,
  fileType: 'File type not supported.',
  generic: 'Something went wrong. Please try again.',
  notFound: 'Content not found.',
  unauthorized: 'Access unauthorized.'
} as const;

// Success messages
export const SUCCESS_MESSAGES = {
  upload: 'File uploaded successfully!',
  save: 'Saved successfully!',
  delete: 'Deleted successfully!',
  copy: 'Copied to clipboard!'
} as const;