import React from 'react';
import { Button } from '@/components/ui/button';
import {
  Mail,
  Phone,
  ExternalLink,
  Github,
  Linkedin,
  Code,
  Briefcase
} from 'lucide-react';

export const FooterBanner: React.FC = () => {
  return (
    <footer className="fixed bottom-0 left-0 right-0 z-40 bg-surface-elevated text-primary border-t border-subtle shadow-2xl fixed-footer">
      <div className="container-magazine px-responsive py-2 sm:py-3 2xl:py-4 footer-compact 2xl:footer-large">
        <div className="flex flex-col lg:flex-row items-center justify-between gap-2 sm:gap-4 lg:gap-8">

          {/* Developer Info & Portfolio CTA */}
          <div className="flex flex-col sm:flex-row items-center gap-2 sm:gap-4 lg:gap-6">
            <div className="flex items-center gap-2 sm:gap-3">
              <div className="w-6 h-6 sm:w-8 sm:h-8 bg-magazine rounded-full flex items-center justify-center flex-shrink-0">
                <Code className="w-3 h-3 sm:w-4 sm:h-4 text-white" />
              </div>
              <div className="text-center sm:text-left">
                <h3 className="text-xs sm:text-sm font-semibold footer-text text-primary">Mauro Frank Lima de Lima</h3>
                <p className="text-xs text-secondary hidden sm:block">Full-Stack Developer • UK</p>
              </div>
            </div>

            <div className="flex items-center gap-1 sm:gap-2 px-2 py-1 sm:px-3 sm:py-1.5 bg-magazine-light border border-magazine rounded-full">
              <Briefcase className="w-2.5 h-2.5 sm:w-3 sm:h-3 text-magazine" />
              <span className="text-xs text-magazine font-medium footer-text">Portfolio Sample</span>
            </div>
          </div>

          {/* Portfolio CTA Message */}
          <div className="text-center lg:text-left flex-1 max-w-xs sm:max-w-md">
            <p className="text-xs text-secondary leading-relaxed footer-text">
              <span className="text-magazine font-medium">Need custom applications?</span> <span className="hidden sm:inline">This project showcases my development skills.</span> Contact me for bespoke websites and applications.
            </p>
          </div>

          {/* Contact & Social Links */}
          <div className="flex flex-col sm:flex-row items-center gap-2 sm:gap-3 lg:gap-4">
            <div className="flex items-center gap-1 sm:gap-2 text-xs text-secondary footer-text">
              <Phone className="w-2.5 h-2.5 sm:w-3 sm:h-3 text-magazine" />
              <a href="tel:+447386797715" className="interactive-text">
                +44 7386 797715
              </a>
            </div>

            <div className="flex items-center gap-1 sm:gap-2 text-xs text-secondary footer-text">
              <Mail className="w-2.5 h-2.5 sm:w-3 sm:h-3 text-magazine" />
              <a href="mailto:<EMAIL>" className="interactive-text">
                <span className="hidden sm:inline"><EMAIL></span>
                <span className="sm:hidden">Email</span>
              </a>
            </div>

            <div className="flex items-center gap-1 sm:gap-2">
              <Button
                variant="ghost"
                size="sm"
                asChild
                className="h-6 px-1.5 sm:h-7 sm:px-2 text-secondary hover-magazine focus-magazine"
              >
                <a href="https://github.com/Webber-Lubenham" target="_blank" rel="noopener noreferrer">
                  <Github className="w-2.5 h-2.5 sm:w-3 sm:h-3" />
                </a>
              </Button>

              <Button
                variant="ghost"
                size="sm"
                asChild
                className="h-6 px-1.5 sm:h-7 sm:px-2 text-secondary hover-magazine focus-magazine"
              >
                <a href="https://linkedin.com/in/mauro-frank-lima-de-lima-a9896a32a" target="_blank" rel="noopener noreferrer">
                  <Linkedin className="w-2.5 h-2.5 sm:w-3 sm:h-3" />
                </a>
              </Button>
            </div>
          </div>
        </div>

        {/* Copyright - Ultra Compact */}
        <div className="text-center mt-1 sm:mt-2 pt-1 sm:pt-2 border-t border-subtle/50">
          <p className="text-xs text-secondary footer-text">
            © 2025 Lubenham News • <span className="hidden sm:inline">Developed by </span>Mauro Frank Lima de Lima
          </p>
        </div>
      </div>
    </footer>
  );
};
