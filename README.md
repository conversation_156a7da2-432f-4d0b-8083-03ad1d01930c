# 📰 Lubenham News - Digital Magazine

Uma revista digital moderna e responsiva para a comunidade de Lubenham, apresentando notícias locais, eventos e atualizações da comunidade de forma elegante e acessível.

![Lubenham News](https://img.shields.io/badge/Status-Active-green)
![React](https://img.shields.io/badge/React-18.3.1-blue)
![TypeScript](https://img.shields.io/badge/TypeScript-5.5.3-blue)
![Vite](https://img.shields.io/badge/Vite-5.4.1-purple)

## ✨ Características Principais

### 🎨 Interface Moderna
- **Página de Capa Hero** - Capa em tela cheia com navegação intuitiva
- **Grade de Conteúdo** - Visualização em miniatura de todas as páginas
- **Visualizador de Páginas** - Exibição individual com navegação por scroll-snap
- **Design Glass Morphism** - Interface moderna com efeitos de blur

### 🔍 Funcionalidades Avançadas
- **Busca Full-Text** - Pesquisa completa em todo o conteúdo da revista
- **Acessibilidade Completa** - Versões em texto para leitores de tela
- **Design Responsivo** - Funciona perfeitamente em mobile, tablet e desktop
- **Lazy Loading** - Carregamento otimizado com Intersection Observer

### 🌐 Integração Supabase
- **Armazenamento de Imagens** - Upload e gerenciamento de assets
- **Base de Dados** - Estrutura para conteúdo dinâmico
- **Hooks Personalizados** - Integração seamless com React

## 🛠️ Stack Tecnológica

### Frontend
- **React 18.3.1** - Biblioteca principal
- **TypeScript 5.5.3** - Tipagem estática
- **Vite 5.4.1** - Build tool e dev server
- **Tailwind CSS** - Framework CSS utilitário
- **React Router** - Roteamento SPA

### Bibliotecas Especializadas
- **Radix UI** - Componentes acessíveis
- **Tanstack Query** - Gerenciamento de estado servidor
- **MiniSearch** - Busca client-side
- **Lucide React** - Ícones modernos
- **React Hook Form** - Formulários performáticos

### Backend & Infraestrutura
- **Supabase** - Backend-as-a-Service
- **PostgreSQL** - Base de dados
- **Storage** - Armazenamento de arquivos

## 📁 Estrutura do Projeto

```
lubenham-news/
├── src/
│   ├── components/
│   │   ├── magazine/          # Componentes da revista
│   │   ├── storage/           # Componentes de upload
│   │   ├── database/          # Componentes de BD
│   │   └── ui/               # Componentes base (shadcn/ui)
│   ├── hooks/                # Hooks personalizados
│   ├── data/                 # Dados da revista
│   ├── types/                # Definições TypeScript
│   └── lib/                  # Utilitários
├── public/
│   ├── lovable-uploads/      # Imagens da revista
│   └── pages/               # Assets de páginas
├── scripts/                 # Scripts de automação
├── supabase/               # Configurações Supabase
└── database/               # Esquemas de BD
```

## 🚀 Começando

### Pré-requisitos
- Node.js 18+ 
- npm ou yarn
- Conta Supabase (opcional)

### Instalação

1. **Clone o repositório**
```bash
git clone <repository-url>
cd lubenham-news
```

2. **Instale as dependências**
```bash
npm install
```

3. **Configure variáveis de ambiente** (opcional)
```bash
cp .env.example .env
# Configure suas chaves do Supabase
```

4. **Inicie o servidor de desenvolvimento**
```bash
npm run dev
```

5. **Acesse a aplicação**
```
http://localhost:5173
```

## 📝 Scripts Disponíveis

```bash
# Desenvolvimento
npm run dev              # Inicia servidor de desenvolvimento

# Build
npm run build           # Build de produção
npm run build:dev       # Build de desenvolvimento
npm run preview         # Preview do build

# Qualidade de Código
npm run lint            # Executa ESLint

# Utilitários
npm run upload-images   # Upload de imagens para Supabase
```

## 📖 Como Usar

### Adicionando Novas Páginas

1. **Adicione as imagens** em `public/lovable-uploads/`
2. **Atualize** `src/data/magazineData.ts`:
   ```typescript
   {
     id: 17,
     title: "Nova Página",
     summary: "Descrição da página",
     imageUrl: "/lovable-uploads/nova-pagina.jpg",
     altText: "Texto alternativo",
     ocrText: "Conteúdo OCR para busca..."
   }
   ```

### Personalizando o Design

O projeto usa um sistema de design personalizado com:
- **Cores**: Verde e creme focados na comunidade
- **Tipografia**: Otimizada para legibilidade
- **Componentes**: Baseados em Radix UI + shadcn/ui

### Configurando Supabase

1. Crie um projeto no [Supabase](https://supabase.com)
2. Configure as variáveis no `.env`
3. Execute as migrações em `database/`
4. Use os hooks em `src/hooks/` para integração

## 🔧 Configuração Avançada

### Upload de Imagens
```bash
# Upload automático para Supabase
npm run upload-images
```

### Busca e Indexação
A busca utiliza MiniSearch para indexação client-side de todo o conteúdo OCR.

### Acessibilidade
- Textos alternativos completos
- Navegação por teclado
- Suporte a leitores de tela
- Contraste otimizado

## 🤝 Contribuindo

1. Fork o projeto
2. Crie uma branch para sua feature (`git checkout -b feature/AmazingFeature`)
3. Commit suas mudanças (`git commit -m 'Add some AmazingFeature'`)
4. Push para a branch (`git push origin feature/AmazingFeature`)
5. Abra um Pull Request

## 📄 Licença

Este projeto está sob a licença MIT. Veja o arquivo `LICENSE` para detalhes.

## 🙏 Agradecimentos

- **Comunidade Lubenham** - Pelo conteúdo e apoio
- **Lovable** - Pela plataforma de desenvolvimento
- **Supabase** - Pela infraestrutura backend
- **Radix UI** - Pelos componentes acessíveis

---

**Lubenham News** - Conectando a comunidade através da informação digital 🌟