export interface Database {
  public: {
    Tables: {
      todos: {
        Row: {
          id: number;
          title: string;
          completed: boolean;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: number;
          title: string;
          completed?: boolean;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: number;
          title?: string;
          completed?: boolean;
          created_at?: string;
          updated_at?: string;
        };
      };
      magazine_pages: {
        Row: {
          id: number;
          page_number: number;
          title: string;
          content: string;
          image_url?: string;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: number;
          page_number: number;
          title: string;
          content: string;
          image_url?: string;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: number;
          page_number?: number;
          title?: string;
          content?: string;
          image_url?: string;
          created_at?: string;
          updated_at?: string;
        };
      };
      articles: {
        Row: {
          id: number;
          title: string;
          content: string;
          author: string;
          category: string;
          published: boolean;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: number;
          title: string;
          content: string;
          author: string;
          category: string;
          published?: boolean;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: number;
          title?: string;
          content?: string;
          author?: string;
          category?: string;
          published?: boolean;
          created_at?: string;
          updated_at?: string;
        };
      };
    };
    Views: {
      [_ in never]: never;
    };
    Functions: {
      [_ in never]: never;
    };
    Enums: {
      [_ in never]: never;
    };
  };
}