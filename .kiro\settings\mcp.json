{"mcpServers": {"supabase": {"command": "uvx", "args": ["mcp-server-supabase@latest"], "env": {"SUPABASE_ACCESS_TOKEN": "********************************************", "FASTMCP_LOG_LEVEL": "ERROR"}, "disabled": false, "autoApprove": ["mcp_supabase_list_projects", "mcp_supabase_get_project", "mcp_supabase_list_tables", "mcp_supabase_execute_sql", "mcp_supabase_get_project_url", "mcp_supabase_get_anon_key"]}}}