import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Search, BookOpen, Menu } from 'lucide-react';
import { magazineInfo, magazinePages } from '@/data/magazineData';

interface MagazineHeroProps {
  onNavigateToContents: () => void;
  onNavigateToPages: () => void;
  onToggleSearch: () => void;
}

export const MagazineHero: React.FC<MagazineHeroProps> = ({
  onNavigateToContents,
  onNavigateToPages,
  onToggleSearch,
}) => {
  const coverPage = magazinePages[0];

  return (
    <section className="relative h-96 2xl:h-[28rem] 3xl:h-[32rem] 4xl:h-[36rem] flex items-center justify-center overflow-hidden rounded-t-lg">
      {/* Background Image */}
      <div className="absolute inset-0 z-0">
        <img
          src={coverPage.imageUrl}
          alt={coverPage.altText}
          className="w-full h-full object-cover"
          fetchpriority="high"
        />
        <div className="absolute inset-0 bg-gradient-to-b from-black/60 via-black/30 to-black/70" />
      </div>



      {/* Hero Content */}
      <div className="relative z-10 text-center max-w-4xl 2xl:max-w-6xl 3xl:max-w-8xl mx-auto px-4 2xl:px-8 3xl:px-12">
        <h1 className="magazine-title text-white mb-4 2xl:mb-6 3xl:mb-8 drop-shadow-lg text-4xl md:text-6xl 2xl:text-7xl 3xl:text-8xl 4xl:text-9xl">
          {magazineInfo.title}
        </h1>

        <p className="magazine-subtitle text-white/90 mb-6 2xl:mb-8 3xl:mb-10 drop-shadow-md text-xl 2xl:text-2xl 3xl:text-3xl">
          {magazineInfo.subtitle}
        </p>

        <div className="flex items-center justify-center gap-6 2xl:gap-8 3xl:gap-10 mb-8 2xl:mb-10 3xl:mb-12 text-white/80">
          <span className="text-lg 2xl:text-xl 3xl:text-2xl font-medium">Issue {magazineInfo.issue}</span>
          <span className="w-1 h-1 2xl:w-1.5 2xl:h-1.5 3xl:w-2 3xl:h-2 bg-white/60 rounded-full"></span>
          <span className="text-lg 2xl:text-xl 3xl:text-2xl">{magazineInfo.date}</span>
        </div>

        <div className="flex flex-col sm:flex-row items-center justify-center gap-4 2xl:gap-6 3xl:gap-8">
          <Button
            onClick={onNavigateToContents}
            className="bg-white/20 hover:bg-white/30 text-white border border-white/30 backdrop-blur-sm px-8 py-3 2xl:px-10 2xl:py-4 3xl:px-12 3xl:py-5 text-lg 2xl:text-xl 3xl:text-2xl"
          >
            <BookOpen className="w-5 h-5 2xl:w-6 2xl:h-6 3xl:w-7 3xl:h-7 mr-2 2xl:mr-3 3xl:mr-4" />
            View Contents
          </Button>

          <Button
            onClick={onNavigateToPages}
            variant="outline"
            className="bg-transparent hover:bg-white/10 text-white border-white/50 hover:border-white/70 px-8 py-3 2xl:px-10 2xl:py-4 3xl:px-12 3xl:py-5 text-lg 2xl:text-xl 3xl:text-2xl"
          >
            <Menu className="w-5 h-5 2xl:w-6 2xl:h-6 3xl:w-7 3xl:h-7 mr-2 2xl:mr-3 3xl:mr-4" />
            Browse Pages
          </Button>
        </div>
      </div>

      {/* Scroll Indicator */}
      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 text-white/60 animate-bounce">
        <div className="flex flex-col items-center gap-2">
          <span className="text-sm">Scroll to explore</span>
          <div className="w-6 h-10 border-2 border-white/40 rounded-full flex items-start justify-center p-2">
            <div className="w-1 h-3 bg-white/60 rounded-full animate-pulse"></div>
          </div>
        </div>
      </div>
    </section>
  );
};