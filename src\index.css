@tailwind base;
@tailwind components;
@tailwind utilities;

/* Lubenham News Design System - Community Magazine Theme */

@layer base {
  :root {
    /* Core community green palette inspired by newsletter */
    --background: 0 0% 100%;
    --foreground: 158 25% 15%;

    --card: 0 0% 100%;
    --card-foreground: 158 25% 15%;

    --popover: 0 0% 100%;
    --popover-foreground: 158 25% 15%;

    /* Primary - Newsletter green */
    --primary: 158 45% 35%;
    --primary-foreground: 0 0% 98%;
    --primary-accent: 158 55% 45%;

    /* Secondary - Warm cream */
    --secondary: 45 25% 92%;
    --secondary-foreground: 158 25% 15%;

    --muted: 45 15% 95%;
    --muted-foreground: 158 15% 45%;

    --accent: 158 35% 85%;
    --accent-foreground: 158 45% 25%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;

    --border: 158 20% 88%;
    --input: 158 15% 94%;
    --ring: 158 45% 35%;

    --radius: 0.75rem;

    /* Custom magazine tokens */
    --newsletter-green: 158 45% 35%;
    --newsletter-green-hover: 158 45% 30%;
    --newsletter-light: 158 35% 85%;
    --newsletter-light-hover: 158 35% 80%;
    --cream-warm: 45 25% 92%;
    --text-soft: 158 15% 45%;

    /* Consistent spacing scale */
    --spacing-xs: 0.5rem;
    --spacing-sm: 1rem;
    --spacing-md: 1.5rem;
    --spacing-lg: 2rem;
    --spacing-xl: 3rem;
    --spacing-2xl: 4rem;
    
    /* Glass morphism effects */
    --glass-bg: 0 0% 100% / 0.8;
    --glass-border: 158 20% 88% / 0.5;
    
    /* Gradients */
    --gradient-hero: linear-gradient(135deg, hsl(158 45% 35%), hsl(158 55% 45%));
    --gradient-card: linear-gradient(145deg, hsl(0 0% 100% / 0.9), hsl(45 25% 92% / 0.8));
    
    /* Shadows */
    --shadow-soft: 0 4px 20px hsl(158 25% 15% / 0.08);
    --shadow-magazine: 0 8px 32px hsl(158 25% 15% / 0.12);
    
    /* Typography */
    --font-magazine: 'Georgia', 'Times New Roman', serif;
    --font-modern: 'Inter', system-ui, sans-serif;

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    /* Dark mode - newsletter evening theme */
    --background: 158 25% 8%;
    --foreground: 158 15% 92%;

    --card: 158 25% 12%;
    --card-foreground: 158 15% 92%;

    --popover: 158 25% 12%;
    --popover-foreground: 158 15% 92%;

    --primary: 158 55% 65%;
    --primary-foreground: 158 25% 8%;
    --primary-accent: 158 65% 75%;

    --secondary: 158 15% 18%;
    --secondary-foreground: 158 15% 92%;

    --muted: 158 10% 15%;
    --muted-foreground: 158 15% 65%;

    --accent: 158 25% 25%;
    --accent-foreground: 158 55% 75%;

    --destructive: 0 75% 55%;
    --destructive-foreground: 158 15% 92%;

    --border: 158 15% 25%;
    --input: 158 15% 18%;
    --ring: 158 55% 65%;
    
    /* Dark mode custom tokens */
    --newsletter-green: 158 55% 65%;
    --newsletter-green-hover: 158 55% 70%;
    --newsletter-light: 158 25% 25%;
    --newsletter-light-hover: 158 25% 30%;
    --cream-warm: 45 15% 18%;
    --text-soft: 158 15% 65%;
    
    --glass-bg: 158 25% 12% / 0.8;
    --glass-border: 158 15% 25% / 0.5;
    
    --gradient-hero: linear-gradient(135deg, hsl(158 35% 25%), hsl(158 45% 35%));
    --gradient-card: linear-gradient(145deg, hsl(158 25% 12% / 0.9), hsl(158 15% 18% / 0.8));
    
    --shadow-soft: 0 4px 20px hsl(158 25% 8% / 0.3);
    --shadow-magazine: 0 8px 32px hsl(158 25% 8% / 0.4);
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
    scroll-behavior: smooth;
    font-family: system-ui, -apple-system, sans-serif;
  }
}

@layer components {
  /* Magazine glass card effect */
  .glass-card {
    @apply backdrop-blur-md bg-white/80 dark:bg-white/5 border border-white/20 shadow-lg;
  }
  
  /* Magazine page scroll snap */
  .magazine-scroll {
    scroll-snap-type: y mandatory;
  }
  
  .magazine-page {
    scroll-snap-align: start;
  }
  
  /* Custom magazine typography */
  .magazine-title {
    font-family: var(--font-magazine);
    @apply text-4xl md:text-6xl font-bold tracking-tight;
  }

  @media (min-width: 1920px) {
    .magazine-title {
      @apply text-7xl;
    }
  }

  @media (min-width: 2560px) {
    .magazine-title {
      @apply text-8xl;
    }
  }

  @media (min-width: 3440px) {
    .magazine-title {
      @apply text-9xl;
    }
  }

  .magazine-subtitle {
    font-family: var(--font-magazine);
    @apply text-lg md:text-xl italic;
  }

  @media (min-width: 1920px) {
    .magazine-subtitle {
      @apply text-2xl;
    }
  }

  @media (min-width: 2560px) {
    .magazine-subtitle {
      @apply text-3xl;
    }
  }

  .magazine-body {
    font-family: var(--font-modern);
    @apply text-base leading-relaxed;
  }

  @media (min-width: 1920px) {
    .magazine-body {
      @apply text-lg;
    }
  }

  @media (min-width: 2560px) {
    .magazine-body {
      @apply text-xl;
    }
  }

  /* Additional typography classes for consistency */
  .text-heading-lg {
    font-family: var(--font-magazine);
    @apply text-2xl md:text-3xl font-bold tracking-tight;
  }

  @media (min-width: 1920px) {
    .text-heading-lg {
      @apply text-4xl;
    }
  }

  @media (min-width: 2560px) {
    .text-heading-lg {
      @apply text-5xl;
    }
  }

  .text-heading-md {
    font-family: var(--font-magazine);
    @apply text-xl md:text-2xl font-semibold tracking-tight;
  }

  @media (min-width: 1920px) {
    .text-heading-md {
      @apply text-3xl;
    }
  }

  @media (min-width: 2560px) {
    .text-heading-md {
      @apply text-4xl;
    }
  }

  .text-heading-sm {
    font-family: var(--font-modern);
    @apply text-lg md:text-xl font-semibold;
  }

  @media (min-width: 1920px) {
    .text-heading-sm {
      @apply text-2xl;
    }
  }

  @media (min-width: 2560px) {
    .text-heading-sm {
      @apply text-3xl;
    }
  }

  .text-body-lg {
    font-family: var(--font-modern);
    @apply text-lg leading-relaxed;
  }

  @media (min-width: 1920px) {
    .text-body-lg {
      @apply text-xl;
    }
  }

  @media (min-width: 2560px) {
    .text-body-lg {
      @apply text-2xl;
    }
  }

  .text-body-sm {
    font-family: var(--font-modern);
    @apply text-sm leading-relaxed;
  }

  @media (min-width: 1920px) {
    .text-body-sm {
      @apply text-base;
    }
  }

  @media (min-width: 2560px) {
    .text-body-sm {
      @apply text-lg;
    }
  }

  /* Fixed Footer Optimizations */
  .fixed-footer {
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
  }

  /* Ensure content doesn't get hidden behind fixed footer */
  body {
    padding-bottom: 0;
  }

  /* Responsive footer adjustments */
  @media (max-width: 640px) {
    .footer-compact {
      padding: 0.75rem 1rem;
    }

    .footer-text {
      font-size: 0.75rem;
    }
  }

  @media (min-width: 1920px) {
    .footer-large {
      padding: 1rem 2rem;
    }
  }
}

@layer utilities {
  /* Magazine-specific utilities */
  .shadow-magazine {
    box-shadow: var(--shadow-magazine);
  }

  .shadow-soft {
    box-shadow: var(--shadow-soft);
  }

  .text-magazine {
    color: hsl(var(--newsletter-green));
  }

  .text-magazine-light {
    color: hsl(var(--newsletter-light));
  }

  .bg-magazine {
    background: var(--gradient-hero);
  }

  .bg-magazine-light {
    background: hsl(var(--newsletter-light));
  }

  .bg-glass {
    background: hsl(var(--glass-bg));
    border: 1px solid hsl(var(--glass-border));
    backdrop-filter: blur(16px);
  }

  /* Consistent hover states */
  .hover-magazine {
    @apply hover:text-magazine hover:bg-magazine-light/10 transition-all duration-200;
  }

  .hover-scale {
    @apply hover:scale-[1.02] transition-transform duration-200;
  }

  /* Consistent focus states */
  .focus-magazine {
    @apply focus:outline-none focus:ring-2 focus:ring-magazine focus:ring-offset-2;
  }

  /* Consistent card styles */
  .card-magazine {
    @apply bg-card border border-border rounded-lg shadow-soft hover:shadow-magazine transition-all duration-200;
  }

  .card-magazine-interactive {
    @apply card-magazine hover-scale cursor-pointer;
  }

  /* Consistent button styles */
  .btn-magazine-primary {
    @apply bg-magazine hover:bg-magazine text-white font-medium px-4 py-2 rounded-lg focus-magazine transition-all duration-200;
  }

  .btn-magazine-secondary {
    @apply bg-magazine-light hover:bg-magazine-light text-magazine font-medium px-4 py-2 rounded-lg focus-magazine transition-all duration-200;
  }

  .btn-magazine-ghost {
    @apply text-foreground hover-magazine font-medium px-4 py-2 rounded-lg focus-magazine;
  }

  /* Consistent spacing patterns */
  .spacing-section {
    @apply py-8 2xl:py-12 3xl:py-16;
  }

  .spacing-container {
    @apply px-4 sm:px-6 2xl:px-12 3xl:px-16;
  }

  .spacing-content {
    @apply space-y-6 2xl:space-y-8 3xl:space-y-10;
  }

  .spacing-grid {
    @apply gap-4 sm:gap-6 2xl:gap-8 3xl:gap-10;
  }

  /* Responsive container widths */
  .container-magazine {
    @apply w-full mx-auto max-w-[1920px] 2xl:max-w-[2560px] 3xl:max-w-none;
  }

  .container-content {
    @apply max-w-4xl 2xl:max-w-6xl 3xl:max-w-8xl mx-auto;
  }

  .container-wide {
    @apply max-w-5xl 2xl:max-w-7xl 3xl:max-w-9xl mx-auto;
  }

  /* Responsive text sizing */
  .text-responsive-xs {
    @apply text-xs sm:text-sm 2xl:text-base 3xl:text-lg;
  }

  .text-responsive-sm {
    @apply text-sm sm:text-base 2xl:text-lg 3xl:text-xl;
  }

  .text-responsive-base {
    @apply text-base sm:text-lg 2xl:text-xl 3xl:text-2xl;
  }

  /* Responsive padding/margin */
  .p-responsive {
    @apply p-4 sm:p-6 2xl:p-8 3xl:p-10;
  }

  .px-responsive {
    @apply px-4 sm:px-6 2xl:px-8 3xl:px-10;
  }

  .py-responsive {
    @apply py-4 sm:py-6 2xl:py-8 3xl:py-10;
  }

  .m-responsive {
    @apply m-4 sm:m-6 2xl:m-8 3xl:m-10;
  }

  .mb-responsive {
    @apply mb-4 sm:mb-6 2xl:mb-8 3xl:mb-10;
  }

  .mt-responsive {
    @apply mt-4 sm:mt-6 2xl:mt-8 3xl:mt-10;
  }

  /* Dark mode enhancements */
  .bg-surface {
    @apply bg-background dark:bg-card;
  }

  .bg-surface-elevated {
    @apply bg-card dark:bg-muted;
  }

  .text-primary {
    @apply text-foreground;
  }

  .text-secondary {
    @apply text-muted-foreground;
  }

  .border-subtle {
    @apply border-border;
  }

  /* Glass morphism for dark mode */
  .glass-dark {
    @apply backdrop-blur-md bg-background/80 dark:bg-card/80 border border-border/20 shadow-lg;
  }

  /* Interactive states for dark mode */
  .interactive-surface {
    @apply bg-surface hover:bg-surface-elevated transition-colors duration-200;
  }

  .interactive-text {
    @apply text-primary hover:text-magazine transition-colors duration-200;
  }

  /* Enhanced interactive states */
  .interactive-card {
    @apply card-magazine hover-scale interactive-surface cursor-pointer;
  }

  .interactive-button {
    @apply transition-all duration-200 transform active:scale-95 hover:shadow-soft;
  }

  .interactive-link {
    @apply interactive-text underline-offset-4 hover:underline;
  }

  /* Focus states with better accessibility */
  .focus-visible-magazine {
    @apply focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-magazine focus-visible:ring-offset-2;
  }

  /* Loading states */
  .loading-shimmer {
    @apply animate-pulse bg-gradient-to-r from-muted via-muted-foreground/20 to-muted;
  }

  /* Smooth transitions for all interactive elements */
  .smooth-transition {
    @apply transition-all duration-300 ease-in-out;
  }

  /* Hover effects for different element types */
  .hover-lift {
    @apply hover:-translate-y-1 hover:shadow-magazine transition-all duration-200;
  }

  .hover-glow {
    @apply hover:shadow-lg hover:shadow-magazine/25 transition-all duration-200;
  }

  .hover-brighten {
    @apply hover:brightness-110 transition-all duration-200;
  }

  /* Visual hierarchy utilities */
  .hierarchy-primary {
    @apply text-magazine font-bold text-heading-lg mb-responsive;
  }

  .hierarchy-secondary {
    @apply text-primary font-semibold text-heading-md mb-responsive;
  }

  .hierarchy-tertiary {
    @apply text-primary font-medium text-heading-sm mb-responsive;
  }

  .hierarchy-body {
    @apply text-secondary text-body-lg leading-relaxed mb-responsive;
  }

  .hierarchy-caption {
    @apply text-secondary text-body-sm leading-relaxed;
  }

  /* Content sections with proper spacing */
  .content-section {
    @apply spacing-section;
  }

  .content-header {
    @apply text-center mb-responsive;
  }

  .content-grid {
    @apply spacing-grid mb-responsive;
  }

  /* Visual emphasis utilities */
  .emphasis-high {
    @apply text-magazine font-bold;
  }

  .emphasis-medium {
    @apply text-primary font-semibold;
  }

  .emphasis-low {
    @apply text-secondary font-normal;
  }

  /* Layout utilities for better visual flow */
  .flow-content > * + * {
    @apply mt-4 sm:mt-6 2xl:mt-8;
  }

  .flow-tight > * + * {
    @apply mt-2 sm:mt-3 2xl:mt-4;
  }

  .flow-loose > * + * {
    @apply mt-6 sm:mt-8 2xl:mt-10;
  }
  
  /* Magazine page layout constraints */
  .magazine-page-container {
    max-width: 100%;
    overflow: hidden;
  }

  .magazine-page-image-container {
    max-width: 100%;
    overflow: hidden;
    display: flex;
    justify-content: center;
  }

  .magazine-page-image {
    max-width: 900px;
    max-height: 800px;
    width: 100%;
    height: auto;
    object-fit: contain;
    display: block;
  }

  @media (min-width: 1920px) {
    .magazine-page-image {
      max-width: 1200px;
      max-height: 900px;
    }
  }

  @media (min-width: 2560px) {
    .magazine-page-image {
      max-width: 1400px;
      max-height: 1000px;
    }
  }

  @media (min-width: 3440px) {
    .magazine-page-image {
      max-width: 1600px;
      max-height: 1200px;
    }
  }

  .magazine-page-text-container {
    max-width: 100%;
    overflow: hidden;
  }

  /* Responsive layout utilities */
  .layout-container {
    @apply container mx-auto px-4;
  }

  .main-content-box {
    @apply bg-white dark:bg-gray-900 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700;
  }

  /* Accessibility improvements */
  .focus-visible {
    @apply focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2;
  }

  /* Mobile-first responsive text */
  .responsive-text-sm {
    @apply text-sm md:text-base;
  }

  .responsive-text-lg {
    @apply text-lg md:text-xl lg:text-2xl;
  }
  
  .magazine-page-text {
    word-wrap: break-word;
    overflow-wrap: break-word;
    word-break: break-word;
    max-width: 100%;
    white-space: pre-wrap;
    hyphens: auto;
    max-height: 600px;
    overflow-y: auto;
  }

  /* Custom Scrollbar Styles */
  .scrollbar-thin {
    scrollbar-width: thin;
  }

  .scrollbar-thin::-webkit-scrollbar {
    width: 6px;
  }

  .scrollbar-thin::-webkit-scrollbar-track {
    background: transparent;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb {
    background-color: rgba(156, 163, 175, 0.5);
    border-radius: 3px;
    transition: background-color 0.2s ease;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb:hover {
    background-color: rgba(156, 163, 175, 0.8);
  }

  .dark .scrollbar-thin::-webkit-scrollbar-thumb {
    background-color: rgba(75, 85, 99, 0.5);
  }

  .dark .scrollbar-thin::-webkit-scrollbar-thumb:hover {
    background-color: rgba(75, 85, 99, 0.8);
  }

  /* Smooth scrolling for sidebar */
  .sidebar-scroll {
    scroll-behavior: smooth;
  }

  /* Fade effect for scrollable content */
  .scroll-fade-top::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 20px;
    background: linear-gradient(to bottom, rgba(255, 255, 255, 1), rgba(255, 255, 255, 0));
    pointer-events: none;
    z-index: 1;
  }

  .dark .scroll-fade-top::before {
    background: linear-gradient(to bottom, rgba(17, 24, 39, 1), rgba(17, 24, 39, 0));
  }

  .scroll-fade-bottom::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 20px;
    background: linear-gradient(to top, rgba(255, 255, 255, 1), rgba(255, 255, 255, 0));
    pointer-events: none;
    z-index: 1;
  }

  .dark .scroll-fade-bottom::after {
    background: linear-gradient(to top, rgba(17, 24, 39, 1), rgba(17, 24, 39, 0));
  }

  /* Large Screen Optimizations */
  @media (min-width: 1920px) {
    .large-screen-container {
      max-width: 1920px;
    }

    .large-screen-text {
      font-size: 1.125rem;
      line-height: 1.75rem;
    }

    .large-screen-spacing {
      padding: 2rem;
    }
  }

  @media (min-width: 2560px) {
    .ultrawide-container {
      max-width: 2560px;
    }

    .ultrawide-text {
      font-size: 1.25rem;
      line-height: 1.875rem;
    }

    .ultrawide-spacing {
      padding: 3rem;
    }
  }

  @media (min-width: 3440px) {
    .superwide-container {
      max-width: none;
    }

    .superwide-text {
      font-size: 1.375rem;
      line-height: 2rem;
    }

    .superwide-spacing {
      padding: 4rem;
    }
  }

  /* Magazine Page Optimizations for Large Screens */
  @media (min-width: 1920px) {
    .magazine-page-image {
      max-width: 1200px;
      max-height: 900px;
    }

    .magazine-grid-large {
      grid-template-columns: repeat(4, 1fr);
      gap: 2rem;
    }
  }

  @media (min-width: 2560px) {
    .magazine-page-image {
      max-width: 1400px;
      max-height: 1000px;
    }

    .magazine-grid-ultrawide {
      grid-template-columns: repeat(5, 1fr);
      gap: 2.5rem;
    }
  }

  @media (min-width: 3440px) {
    .magazine-page-image {
      max-width: 1600px;
      max-height: 1200px;
    }

    .magazine-grid-superwide {
      grid-template-columns: repeat(6, 1fr);
      gap: 3rem;
    }
  }
}