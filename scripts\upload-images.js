import { createClient } from '@supabase/supabase-js';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Configuração do Supabase
const supabaseUrl = 'https://vxcwvzrdzlvnsvudwfnr.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InZ4Y3d2enJkemx2bnN2dWR3Zm5yIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1NDMwNjA2NCwiZXhwIjoyMDY5ODgyMDY0fQ.W0MdefZdeHZ2z1sBKQBlrUt_nUT99yGCbTWKux3rTZ0';

const supabase = createClient(supabaseUrl, supabaseKey);

// Lista das imagens para upload
const imagePaths = [
  "C:\\Users\\<USER>\\Documents\\img20250804_11243848.jpg",
  "C:\\Users\\<USER>\\Documents\\img20250804_11265445.jpg",
  "C:\\Users\\<USER>\\Documents\\img20250804_11300092.jpg",
  "C:\\Users\\<USER>\\Documents\\img20250804_11322270.jpg",
  "C:\\Users\\<USER>\\Documents\\img20250804_11343353.jpg",
  "C:\\Users\\<USER>\\Documents\\img20250804_11364245.jpg",
  "C:\\Users\\<USER>\\Documents\\img20250804_11403233.jpg",
  "C:\\Users\\<USER>\\Documents\\img20250804_11513387.jpg",
  "C:\\Users\\<USER>\\Documents\\img20250804_11532847.jpg",
  "C:\\Users\\<USER>\\Documents\\img20250804_11552139.jpg",
  "C:\\Users\\<USER>\\Documents\\img20250804_12211122.jpg",
  "C:\\Users\\<USER>\\Documents\\img20250804_12233061.jpg",
  "C:\\Users\\<USER>\\Documents\\img20250804_12242970.jpg",
  "C:\\Users\\<USER>\\Documents\\img20250804_12253712.jpg",
  "C:\\Users\\<USER>\\Documents\\img20250804_12273075.jpg",
  "C:\\Users\\<USER>\\Documents\\img20250804_12283724.jpg"
];

const bucketName = 'agosto25';

async function uploadImage(imagePath, index) {
  try {
    console.log(`\n📤 Fazendo upload da imagem ${index + 1}/16: ${path.basename(imagePath)}`);
    
    // Verificar se o arquivo existe
    if (!fs.existsSync(imagePath)) {
      console.error(`❌ Arquivo não encontrado: ${imagePath}`);
      return null;
    }

    // Ler o arquivo
    const fileBuffer = fs.readFileSync(imagePath);
    const fileName = path.basename(imagePath);
    
    // Gerar nome único para o arquivo
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const uniqueFileName = `page-${(index + 10).toString().padStart(2, '0')}-${timestamp}.jpg`;

    console.log(`📁 Nome do arquivo no storage: ${uniqueFileName}`);

    // Fazer upload para o Supabase Storage
    const { data, error } = await supabase.storage
      .from(bucketName)
      .upload(uniqueFileName, fileBuffer, {
        contentType: 'image/jpeg',
        cacheControl: '3600',
        upsert: false
      });

    if (error) {
      console.error(`❌ Erro no upload: ${error.message}`);
      return null;
    }

    // Obter URL pública
    const { data: { publicUrl } } = supabase.storage
      .from(bucketName)
      .getPublicUrl(data.path);

    console.log(`✅ Upload concluído!`);
    console.log(`🔗 URL pública: ${publicUrl}`);

    return {
      originalPath: imagePath,
      storagePath: data.path,
      publicUrl: publicUrl,
      fileName: uniqueFileName
    };

  } catch (error) {
    console.error(`❌ Erro inesperado no upload de ${imagePath}:`, error.message);
    return null;
  }
}

async function uploadAllImages() {
  console.log('🚀 Iniciando upload de 16 imagens para o Supabase Storage...');
  console.log(`📦 Bucket: ${bucketName}`);
  console.log(`🌐 Projeto: ${supabaseUrl}`);
  
  const results = [];
  const successful = [];
  const failed = [];

  for (let i = 0; i < imagePaths.length; i++) {
    const result = await uploadImage(imagePaths[i], i);
    
    if (result) {
      successful.push(result);
    } else {
      failed.push(imagePaths[i]);
    }
    
    results.push(result);
    
    // Pequena pausa entre uploads
    if (i < imagePaths.length - 1) {
      await new Promise(resolve => setTimeout(resolve, 500));
    }
  }

  console.log('\n📊 RESUMO DO UPLOAD:');
  console.log(`✅ Sucessos: ${successful.length}`);
  console.log(`❌ Falhas: ${failed.length}`);
  
  if (successful.length > 0) {
    console.log('\n🎉 IMAGENS ENVIADAS COM SUCESSO:');
    successful.forEach((result, index) => {
      console.log(`${index + 1}. ${result.fileName}`);
      console.log(`   URL: ${result.publicUrl}`);
    });
  }

  if (failed.length > 0) {
    console.log('\n💥 FALHAS NO UPLOAD:');
    failed.forEach((failedPath, index) => {
      console.log(`${index + 1}. ${path.basename(failedPath)}`);
    });
  }

  // Salvar URLs em arquivo JSON
  if (successful.length > 0) {
    const outputFile = path.join(__dirname, '..', 'uploaded-images.json');
    fs.writeFileSync(outputFile, JSON.stringify(successful, null, 2));
    console.log(`\n💾 URLs salvas em: ${outputFile}`);
  }

  console.log('\n🏁 Upload concluído!');
}

// Executar o script
uploadAllImages().catch(console.error);