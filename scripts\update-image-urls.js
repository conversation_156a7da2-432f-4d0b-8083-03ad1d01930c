import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Ler o arquivo JSON com as URLs das imagens
const uploadedImagesPath = path.join(__dirname, '..', 'uploaded-images.json');
const uploadedImages = JSON.parse(fs.readFileSync(uploadedImagesPath, 'utf8'));

// Ler o arquivo de dados da revista
const magazineDataPath = path.join(__dirname, '..', 'src', 'data', 'magazineData.ts');
let magazineDataContent = fs.readFileSync(magazineDataPath, 'utf8');

console.log('🔄 Atualizando URLs das imagens no arquivo de dados da revista...');

// Mapear as imagens por página (começando da página 10)
uploadedImages.forEach((image, index) => {
  const pageNumber = index + 10;
  const oldUrlPattern = new RegExp(`imageUrl: "[^"]*",\\s*altText: "Page ${pageNumber}`, 'g');
  const newUrl = `imageUrl: "${image.publicUrl}",\n    altText: "Page ${pageNumber}`;
  
  if (magazineDataContent.match(oldUrlPattern)) {
    magazineDataContent = magazineDataContent.replace(oldUrlPattern, newUrl);
    console.log(`✅ Página ${pageNumber}: URL atualizada`);
  } else {
    console.log(`⚠️  Página ${pageNumber}: Padrão não encontrado`);
  }
});

// Salvar o arquivo atualizado
fs.writeFileSync(magazineDataPath, magazineDataContent);

console.log('\n🎉 Todas as URLs foram atualizadas com sucesso!');
console.log('📁 Arquivo atualizado:', magazineDataPath);

// Verificar se todas as URLs foram atualizadas
const updatedContent = fs.readFileSync(magazineDataPath, 'utf8');
const supabaseUrls = (updatedContent.match(/supabase\.co\/storage/g) || []).length;

console.log(`\n📊 Estatísticas:`);
console.log(`- Imagens enviadas: ${uploadedImages.length}`);
console.log(`- URLs do Supabase encontradas no arquivo: ${supabaseUrls}`);

if (supabaseUrls >= uploadedImages.length) {
  console.log('✅ Todas as URLs foram atualizadas corretamente!');
} else {
  console.log('⚠️  Algumas URLs podem não ter sido atualizadas. Verifique manualmente.');
}