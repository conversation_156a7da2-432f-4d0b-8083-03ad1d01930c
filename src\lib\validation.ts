// Utilitários de validação para Lubenham News

import { PATTERNS, SUPABASE_CONFIG } from './constants';

/**
 * Valida endereço de email
 */
export const isValidEmail = (email: string): boolean => {
  return PATTERNS.email.test(email.trim());
};

/**
 * Valida número de telefone
 */
export const isValidPhone = (phone: string): boolean => {
  return PATTERNS.phone.test(phone.trim());
};

/**
 * Valida URL
 */
export const isValidUrl = (url: string): boolean => {
  return PATTERNS.url.test(url.trim());
};

/**
 * Valida se o arquivo é uma imagem válida
 */
export const isValidImageFile = (file: File): { valid: boolean; error?: string } => {
  // Verifica tipo de arquivo
  if (!SUPABASE_CONFIG.allowedFileTypes.includes(file.type)) {
    return {
      valid: false,
      error: `Tipo de arquivo não suportado. Use: ${SUPABASE_CONFIG.allowedFileTypes.join(', ')}`
    };
  }

  // Verifica tamanho do arquivo
  if (file.size > SUPABASE_CONFIG.maxFileSize) {
    const maxSizeMB = SUPABASE_CONFIG.maxFileSize / 1024 / 1024;
    return {
      valid: false,
      error: `Arquivo muito grande. Máximo ${maxSizeMB}MB`
    };
  }

  return { valid: true };
};

/**
 * Valida se uma string não está vazia
 */
export const isNotEmpty = (value: string): boolean => {
  return value.trim().length > 0;
};

/**
 * Valida comprimento mínimo de string
 */
export const hasMinLength = (value: string, minLength: number): boolean => {
  return value.trim().length >= minLength;
};

/**
 * Valida comprimento máximo de string
 */
export const hasMaxLength = (value: string, maxLength: number): boolean => {
  return value.trim().length <= maxLength;
};

/**
 * Valida se um número está dentro de um intervalo
 */
export const isInRange = (value: number, min: number, max: number): boolean => {
  return value >= min && value <= max;
};

/**
 * Valida se uma data é válida
 */
export const isValidDate = (date: string | Date): boolean => {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  return dateObj instanceof Date && !isNaN(dateObj.getTime());
};

/**
 * Valida se uma data está no futuro
 */
export const isFutureDate = (date: string | Date): boolean => {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  return isValidDate(dateObj) && dateObj.getTime() > Date.now();
};

/**
 * Valida se uma data está no passado
 */
export const isPastDate = (date: string | Date): boolean => {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  return isValidDate(dateObj) && dateObj.getTime() < Date.now();
};

/**
 * Valida formato de slug (URL-friendly)
 */
export const isValidSlug = (slug: string): boolean => {
  const slugPattern = /^[a-z0-9]+(?:-[a-z0-9]+)*$/;
  return slugPattern.test(slug);
};

/**
 * Valida se um ID de página é válido
 */
export const isValidPageId = (pageId: number): boolean => {
  return Number.isInteger(pageId) && pageId >= 1 && pageId <= 16;
};

/**
 * Valida se uma string contém apenas números
 */
export const isNumeric = (value: string): boolean => {
  return /^\d+$/.test(value);
};

/**
 * Valida se uma string contém apenas letras
 */
export const isAlphabetic = (value: string): boolean => {
  return /^[a-zA-ZÀ-ÿ\s]+$/.test(value);
};

/**
 * Valida se uma string é alfanumérica
 */
export const isAlphanumeric = (value: string): boolean => {
  return /^[a-zA-Z0-9À-ÿ\s]+$/.test(value);
};

/**
 * Valida senha forte
 */
export const isStrongPassword = (password: string): { valid: boolean; errors: string[] } => {
  const errors: string[] = [];

  if (password.length < 8) {
    errors.push('Deve ter pelo menos 8 caracteres');
  }

  if (!/[a-z]/.test(password)) {
    errors.push('Deve conter pelo menos uma letra minúscula');
  }

  if (!/[A-Z]/.test(password)) {
    errors.push('Deve conter pelo menos uma letra maiúscula');
  }

  if (!/\d/.test(password)) {
    errors.push('Deve conter pelo menos um número');
  }

  if (!/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
    errors.push('Deve conter pelo menos um caractere especial');
  }

  return {
    valid: errors.length === 0,
    errors
  };
};

/**
 * Valida objeto de formulário genérico
 */
export const validateForm = <T extends Record<string, any>>(
  data: T,
  rules: Record<keyof T, (value: any) => boolean | string>
): { valid: boolean; errors: Partial<Record<keyof T, string>> } => {
  const errors: Partial<Record<keyof T, string>> = {};

  for (const [field, rule] of Object.entries(rules)) {
    const result = rule(data[field as keyof T]);
    
    if (typeof result === 'string') {
      errors[field as keyof T] = result;
    } else if (!result) {
      errors[field as keyof T] = 'Campo inválido';
    }
  }

  return {
    valid: Object.keys(errors).length === 0,
    errors
  };
};

/**
 * Sanitiza string removendo caracteres perigosos
 */
export const sanitizeString = (input: string): string => {
  return input
    .replace(/[<>]/g, '') // Remove < e >
    .replace(/javascript:/gi, '') // Remove javascript:
    .replace(/on\w+=/gi, '') // Remove event handlers
    .trim();
};

/**
 * Valida se uma cor é válida (hex)
 */
export const isValidHexColor = (color: string): boolean => {
  return /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/.test(color);
};