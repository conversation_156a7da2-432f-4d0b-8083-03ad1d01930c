import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { ArrowUp, ArrowRight, ChevronDown, ChevronUp } from 'lucide-react';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { magazinePages } from '@/data/magazineData';
import { useInView } from 'react-intersection-observer';

// Utility function to sanitize OCR text
const sanitizeText = (text: string): string => {
  return text
    .replace(/&#39;/g, "'")
    .replace(/&quot;/g, '"')
    .replace(/&amp;/g, '&')
    .replace(/&lt;/g, '<')
    .replace(/&gt;/g, '>')
    .replace(/&nbsp;/g, ' ')
    .trim();
};

interface PageViewerProps {
  onScrollToTop: () => void;
}

export const PageViewer: React.FC<PageViewerProps> = ({ onScrollToTop }) => {
  return (
    <section className="spacing-container spacing-section magazine-scroll">
      <div className="container-content">
        <div className="content-header">
          <h2 className="hierarchy-primary">
            Magazine Pages
          </h2>
          <p className="hierarchy-body max-w-2xl 2xl:max-w-3xl 3xl:max-w-4xl mx-auto">
            Read through each page of the magazine. Text versions are provided for accessibility and search.
          </p>
        </div>

        <div className="spacing-content">
          {magazinePages.map((page, index) => (
            <MagazinePage
              key={page.id}
              page={page}
              isLast={index === magazinePages.length - 1}
              onScrollToTop={onScrollToTop}
            />
          ))}
        </div>
      </div>
    </section>
  );
};

interface MagazinePageProps {
  page: typeof magazinePages[0];
  isLast: boolean;
  onScrollToTop: () => void;
}

const MagazinePage: React.FC<MagazinePageProps> = ({ page, isLast, onScrollToTop }) => {
  const { ref, inView } = useInView({
    threshold: 0.3,
    triggerOnce: true,
  });

  const [isTextOpen, setIsTextOpen] = React.useState(false);

  return (
    <div 
      ref={ref}
      id={`page-${page.id}`}
      className={`magazine-page transition-all duration-700 ${
        inView ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-8'
      }`}
    >
      <Card className="glass-card shadow-magazine overflow-hidden card-magazine">
        <CardContent className="p-0 magazine-page-container">
          {/* Page Header */}
          <div className="bg-magazine text-white p-4">
            <div className="flex items-center justify-between">
              <div>
                <Badge variant="secondary" className="bg-white/20 text-white mb-2">
                  Page {page.id}
                </Badge>
                <h3 className="text-xl font-bold">{page.title}</h3>
              </div>
            </div>
          </div>

          {/* Page Image */}
          <div className="relative magazine-page-image-container">
            <img
              src={page.imageUrl}
              alt={page.altText}
              className="magazine-page-image w-full max-w-[900px] 2xl:max-w-[1200px] 3xl:max-w-[1400px] 4xl:max-w-[1600px] mx-auto h-auto object-contain"
              loading={page.id <= 3 ? 'eager' : 'lazy'}
              style={{
                maxHeight: window.innerWidth >= 2560 ? '1000px' :
                          window.innerWidth >= 1920 ? '900px' : '800px'
              }}
            />
          </div>

          {/* Page Content */}
          <div className="p-6 2xl:p-8 3xl:p-10 space-y-4 2xl:space-y-6 3xl:space-y-8">
            <p className="text-body-lg text-muted-foreground">
              {page.summary}
            </p>

            {/* Text Version Collapsible */}
            <Collapsible open={isTextOpen} onOpenChange={setIsTextOpen}>
              <CollapsibleTrigger asChild>
                <Button
                  variant="outline"
                  className="w-full justify-between hover:bg-accent"
                >
                  <span>Text version (for accessibility & search)</span>
                  {isTextOpen ? (
                    <ChevronUp className="w-4 h-4" />
                  ) : (
                    <ChevronDown className="w-4 h-4" />
                  )}
                </Button>
              </CollapsibleTrigger>
              <CollapsibleContent className="mt-4">
                <div className="magazine-page-text-container p-4 bg-muted/50 rounded-lg">
                  <pre className="magazine-page-text whitespace-pre-wrap text-sm font-mono leading-relaxed break-words overflow-wrap-anywhere">
                    {sanitizeText(page.ocrText)}
                  </pre>
                </div>
              </CollapsibleContent>
            </Collapsible>

            {/* Navigation */}
            <div className="flex items-center justify-between pt-4 border-t">
              <Button
                onClick={onScrollToTop}
                variant="outline"
                size="sm"
                className="text-magazine border-magazine hover:bg-magazine hover:text-white"
              >
                <ArrowUp className="w-4 h-4 mr-2" />
                Back to Top
              </Button>
              
              {!isLast && (
                <Button
                  onClick={() => {
                    const nextPageId = page.id + 1;
                    const nextPageElement = document.getElementById(`page-${nextPageId}`);
                    nextPageElement?.scrollIntoView({ behavior: 'smooth' });
                  }}
                  className="bg-magazine hover:bg-magazine/90"
                >
                  Next Page
                  <ArrowRight className="w-4 h-4 ml-2" />
                </Button>
              )}
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};